#!/usr/bin/env python3
"""
Path management utility for organizing files by process ID with timestamps.

This module provides utilities to create and manage the new folder structure:
- CSV files: /downloads/<timestamp-processID>/
- Processed files: /downloads/<timestamp-processID>/processed/
"""

import os
import time
from datetime import datetime, timezone
from typing import Optional


class PathManager:
    """Manages file paths with timestamp-processID folder structure."""
    
    def __init__(self, base_downloads_dir: str = "/home/<USER>/10x-sales-agent/downloads"):
        """
        Initialize PathManager with base downloads directory.
        
        Args:
            base_downloads_dir: Base directory for all downloads
        """
        self.base_downloads_dir = base_downloads_dir
        self._session_folder = None
        self._process_id = None
        self._timestamp = None
    
    def create_session_folder(self, process_id: str) -> str:
        """
        Create a unique timestamp-processID folder for a processing session.
        
        Args:
            process_id: Unique process identifier
            
        Returns:
            Path to the created session folder
        """
        # Generate timestamp in format YYYYMMDD_HHMMSS
        self._timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
        self._process_id = process_id
        
        # Create folder name: timestamp-processID
        folder_name = f"{self._timestamp}-{process_id}"
        self._session_folder = os.path.join(self.base_downloads_dir, folder_name)
        
        # Create the directory
        os.makedirs(self._session_folder, exist_ok=True)
        
        return self._session_folder
    
    def get_session_folder(self) -> Optional[str]:
        """
        Get the current session folder path.
        
        Returns:
            Path to session folder or None if not created yet
        """
        return self._session_folder
    
    def get_downloads_path(self) -> str:
        """
        Get the downloads path for the current session.
        
        Returns:
            Path where CSV files should be downloaded
            
        Raises:
            RuntimeError: If session folder hasn't been created yet
        """
        if not self._session_folder:
            raise RuntimeError("Session folder not created. Call create_session_folder() first.")
        return self._session_folder
    
    def get_processed_path(self) -> str:
        """
        Get the processed files path for the current session.
        
        Returns:
            Path where processed files should be moved
            
        Raises:
            RuntimeError: If session folder hasn't been created yet
        """
        if not self._session_folder:
            raise RuntimeError("Session folder not created. Call create_session_folder() first.")
        
        processed_path = os.path.join(self._session_folder, "processed")
        os.makedirs(processed_path, exist_ok=True)
        return processed_path
    
    def get_process_id(self) -> Optional[str]:
        """Get the current process ID."""
        return self._process_id
    
    def get_timestamp(self) -> Optional[str]:
        """Get the current session timestamp."""
        return self._timestamp
    
    @staticmethod
    def create_timestamped_filename(original_filename: str, timestamp: Optional[int] = None) -> str:
        """
        Create a timestamped filename to avoid conflicts.
        
        Args:
            original_filename: Original filename
            timestamp: Optional timestamp (uses current time if not provided)
            
        Returns:
            Timestamped filename
        """
        if timestamp is None:
            timestamp = int(time.time())
        
        name, ext = os.path.splitext(original_filename)
        return f"{name}_{timestamp}{ext}"
    
    @staticmethod
    def is_session_folder(folder_name: str) -> bool:
        """
        Check if a folder name follows the timestamp-processID pattern.
        
        Args:
            folder_name: Folder name to check
            
        Returns:
            True if folder follows the pattern, False otherwise
        """
        # Pattern: YYYYMMDD_HHMMSS-<process_id>
        parts = folder_name.split('-', 1)
        if len(parts) != 2:
            return False
        
        timestamp_part = parts[0]
        # Check if timestamp part matches YYYYMMDD_HHMMSS format
        if len(timestamp_part) != 15 or timestamp_part[8] != '_':
            return False
        
        try:
            datetime.strptime(timestamp_part, '%Y%m%d_%H%M%S')
            return True
        except ValueError:
            return False


# Global instance for backward compatibility
_global_path_manager = None


def get_path_manager() -> PathManager:
    """Get the global PathManager instance."""
    global _global_path_manager
    if _global_path_manager is None:
        _global_path_manager = PathManager()
    return _global_path_manager


def initialize_session_paths(process_id: str) -> tuple[str, str]:
    """
    Initialize session paths for a process.
    
    Args:
        process_id: Unique process identifier
        
    Returns:
        Tuple of (downloads_path, processed_path)
    """
    path_manager = get_path_manager()
    path_manager.create_session_folder(process_id)
    return path_manager.get_downloads_path(), path_manager.get_processed_path()


def get_current_downloads_path() -> str:
    """
    Get the current session's downloads path.
    
    Returns:
        Downloads path for current session
        
    Raises:
        RuntimeError: If no session is active
    """
    return get_path_manager().get_downloads_path()


def get_current_processed_path() -> str:
    """
    Get the current session's processed files path.
    
    Returns:
        Processed files path for current session
        
    Raises:
        RuntimeError: If no session is active
    """
    return get_path_manager().get_processed_path()


def get_legacy_downloads_path() -> str:
    """
    Get the legacy downloads path for backward compatibility.
    
    Returns:
        Legacy downloads path
    """
    return "/home/<USER>/10x-sales-agent/downloads"


def get_legacy_processed_path() -> str:
    """
    Get the legacy processed path for backward compatibility.
    
    Returns:
        Legacy processed path
    """
    return "/home/<USER>/10x-sales-agent/downloads/processed"
