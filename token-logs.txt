================================================================================
LLM TOKEN USAGE ANALYSIS REPORT
================================================================================
Generated on: 2025-07-28 17:50:43

OVERALL SUMMARY
----------------------------------------
Total Tasks Analyzed: 4
Successful Tasks: 3
Failed Tasks: 1
Total LLM Calls: 41
Total Tokens Used: 377,551
Average Tokens per Call: 9208.6
Average Tokens per Task: 94387.8

TASK BREAKDOWN
----------------------------------------

1. log-0722.txt_session_1_4f21 ✅ SUCCESS
   Description: Browser automation: toys search, CSV export, 4737 results (log-0722.txt)
   Time Range: Jul 22 18:34:42 → Jul 22 18:35:23
   Total Tokens: 77,412
   LLM Calls: 8
   Avg Tokens/Call: 9676.5
   Individual Calls:
      1. Jul 22 18:34:42 | 8,593 tk | 📷 | No step
      2. Jul 22 18:35:00 | 11,519 tk | 📷 | No step
      3. Jul 22 18:35:21 | 9,550 tk | 📷 | Step 14
      4. Jul 22 18:35:22 | 9,550 tk | 📷 | Step 14
      5. Jul 22 18:35:23 | 9,550 tk | 📷 | Step 14
      6. Jul 22 18:35:23 | 9,550 tk | 📷 | Step 14
      7. Jul 22 18:35:23 | 9,550 tk | 📷 | Step 14
      8. Jul 22 18:35:23 | 9,550 tk | 📷 | Step 14

2. log-0722.txt_session_2_4f21 ✅ SUCCESS
   Description: Browser automation: toys search, CSV export, 4737 results (log-0722.txt)
   Time Range: Jul 22 18:35:21 → Jul 22 18:35:23
   Total Tokens: 35,736
   LLM Calls: 6
   Avg Tokens/Call: 5956.0
   Individual Calls:
      1. Jul 22 18:35:21 | 5,956 tk | 📷 | Step 1
      2. Jul 22 18:35:22 | 5,956 tk | 📷 | Step 1
      3. Jul 22 18:35:23 | 5,956 tk | 📷 | Step 1
      4. Jul 22 18:35:23 | 5,956 tk | 📷 | Step 1
      5. Jul 22 18:35:23 | 5,956 tk | 📷 | Step 1
      6. Jul 22 18:35:23 | 5,956 tk | 📷 | Step 1

3. logs-0728.txt_session_1_da8c ❌ FAILED
   Description: Browser automation: paper search, CSV export, 1090 results (logs-0728.txt)
   Time Range: Jul 28 16:30:57 → Jul 28 16:33:27
   Total Tokens: 225,013
   LLM Calls: 21
   Avg Tokens/Call: 10714.9
   Call Summary (large task):
     First Call: Jul 28 16:30:57 | 9,321 tk
     Last Call:  Jul 28 16:33:27 | 11,792 tk
     Min Tokens: 9,154 tk
     Max Tokens: 12,144 tk
     Step Range: 11 → 27
     Calls with Images: 21/21

4. logs-0728.txt_session_2_da8c ✅ SUCCESS
   Description: Browser automation: paper search, CSV export, 1090 results (logs-0728.txt)
   Time Range: Jul 28 16:33:22 → Jul 28 16:33:27
   Total Tokens: 39,390
   LLM Calls: 6
   Avg Tokens/Call: 6565.0
   Individual Calls:
      1. Jul 28 16:33:22 | 6,565 tk | 📷 | Step 1
      2. Jul 28 16:33:25 | 6,565 tk | 📷 | Step 1
      3. Jul 28 16:33:27 | 6,565 tk | 📷 | Step 1
      4. Jul 28 16:33:27 | 6,565 tk | 📷 | Step 1
      5. Jul 28 16:33:27 | 6,565 tk | 📷 | Step 1
      6. Jul 28 16:33:27 | 6,565 tk | 📷 | Step 1

================================================================================
COST ESTIMATION (Approximate)
----------------------------------------
Claude-3.5-Sonnet   : $1.13
GPT-4               : $11.33
GPT-4o              : $0.94

Note: These are rough estimates for input tokens only.
Actual costs may vary based on model, output tokens, and pricing changes.

================================================================================
KEY INSIGHTS & ANALYSIS
================================================================================

TASK PERFORMANCE ANALYSIS:
- Task 1 (Jul 22): Successful toys search with 8 LLM calls, 77K tokens
- Task 2 (Jul 22): Successful toys search continuation with 6 calls, 36K tokens
- Task 3 (Jul 28): Failed paper search with 21 calls, 225K tokens (59% of total usage)
- Task 4 (Jul 28): Successful paper search with 6 calls, 39K tokens

EFFICIENCY PATTERNS:
- Successful tasks averaged 6-8 LLM calls
- Failed task required 21 calls (2.6x more than successful tasks)
- Failed task used 225K tokens vs 39K for successful retry (5.7x difference)
- All calls included images (📷), indicating vision-based browser automation

TOKEN USAGE BY SEARCH TYPE:
- Toys search (игрушки): 113,148 tokens across 2 successful tasks
- Paper search (бумажный): 264,403 tokens (1 failed + 1 successful task)
- Paper search was more challenging, requiring more iterations

STEP PROGRESSION ANALYSIS:
- Successful tasks typically completed in Steps 1-20
- Failed task reached Step 27, indicating multiple retry attempts
- Step progression suggests page stability issues in failed task

RECOMMENDATIONS:
1. Investigate why paper search required more attempts than toys search
2. Consider implementing better error recovery for page stability issues
3. Monitor tasks that exceed 15 LLM calls as potential failure indicators
4. The 225K token failed task represents significant cost - optimize retry logic