#!/usr/bin/env python3
"""
Test script to simulate the API CSV processing flow
"""

import os
import glob
import shutil

def simulate_api_csv_processing():
    """Simulate the exact logic from the fixed _run_search_process function"""
    
    downloads_path = "/home/<USER>/10x-sales-agent/downloads"
    
    print("🔍 Simulating API CSV processing flow...")
    print("=" * 50)
    
    try:
        # Step 1: Look for CSV files in main downloads directory
        print("Step 1: Looking for downloaded CSV file")
        
        csv_files = []
        all_files_found = glob.glob(os.path.join(downloads_path, "*.csv"))
        print(f"DEBUG: Found {len(all_files_found)} total CSV files in downloads_path")
        
        for file in all_files_found:
            print(f"DEBUG: Checking file: {file}")
            # Skip files in processed subdirectory
            if 'processed' not in file:
                csv_files.append(file)
                print(f"DEBUG: Added file to csv_files: {file}")
            else:
                print(f"DEBUG: Skipped processed file: {file}")

        print(f"DEBUG: Found {len(csv_files)} CSV files in main downloads directory")

        # Step 2: If no CSV files in main directory, look in processed directory
        if not csv_files:
            print("No CSV files in main downloads directory, checking processed directory")
            
            processed_dir = os.path.join(downloads_path, "processed")
            if os.path.exists(processed_dir):
                processed_files = glob.glob(os.path.join(processed_dir, "*.csv"))
                print(f"DEBUG: Found {len(processed_files)} CSV files in processed directory")
                
                if processed_files:
                    # Get the most recent processed file
                    latest_processed = max(processed_files, key=os.path.getctime)
                    print(f"DEBUG: Most recent processed file: {latest_processed}")
                    
                    # Copy it back to main downloads directory for processing
                    filename = os.path.basename(latest_processed)
                    copied_file = os.path.join(downloads_path, f"processing_{filename}")
                    shutil.copy2(latest_processed, copied_file)
                    
                    print(f"✅ Copied processed file back for API processing: {copied_file}")
                    
                    latest_file = copied_file
                else:
                    raise Exception("No CSV files found in downloads or processed directories")
            else:
                raise Exception("No CSV file found in downloads directory and processed directory does not exist")
        else:
            # Get the most recent file from main directory
            latest_file = max(csv_files, key=os.path.getctime)
            print(f"✅ Found CSV file in main directory: {latest_file}")

        # Step 3: Simulate processing
        print(f"\nStep 2: Processing CSV file: {os.path.basename(latest_file)}")
        
        # Verify file is readable
        with open(latest_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            line_count = sum(1 for _ in f) + 1
        
        print(f"✅ File is readable")
        print(f"✅ Lines: {line_count:,}")
        print(f"✅ Header: {first_line[:80]}...")
        
        # Simulate successful processing
        print(f"✅ CSV processing would succeed")
        
        # Step 4: Clean up the CSV file after successful processing
        print(f"\nStep 3: Cleaning up CSV file")
        
        filename = os.path.basename(latest_file)
        
        # If this was a copied file from processed directory, just delete it
        if filename.startswith("processing_"):
            os.remove(latest_file)
            print(f"✅ Removed temporary processing file: {latest_file}")
        else:
            # This was a new file, move it to processed directory
            processed_dir = os.path.join(downloads_path, "processed")
            os.makedirs(processed_dir, exist_ok=True)

            processed_file_path = os.path.join(processed_dir, filename)

            # Check if file already exists in processed directory
            if os.path.exists(processed_file_path):
                # Add timestamp to avoid conflicts
                import time
                timestamp = int(time.time())
                name, ext = os.path.splitext(filename)
                new_filename = f"{name}_{timestamp}{ext}"
                processed_file_path = os.path.join(processed_dir, new_filename)
                print(f"File exists in processed, using new name: {new_filename}")

            # Move the file to processed directory
            shutil.move(latest_file, processed_file_path)
            print(f"✅ Moved CSV file to processed directory: {processed_file_path}")
        
        print(f"\n🎉 API CSV processing flow completed successfully!")
        
        # Verify final state
        print(f"\nFinal state verification:")
        remaining_csv = glob.glob(os.path.join(downloads_path, "*.csv"))
        remaining_csv = [f for f in remaining_csv if 'processed' not in f]
        
        if remaining_csv:
            print(f"⚠️  {len(remaining_csv)} CSV files remain in downloads:")
            for f in remaining_csv:
                print(f"  - {os.path.basename(f)}")
        else:
            print(f"✅ Downloads directory is clean")
        
        processed_files = glob.glob(os.path.join(downloads_path, "processed", "*.csv"))
        print(f"✅ Processed directory has {len(processed_files)} files")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in API CSV processing flow: {e}")
        return False

if __name__ == "__main__":
    success = simulate_api_csv_processing()
    if success:
        print(f"\n✅ Test passed - API will work correctly!")
    else:
        print(f"\n❌ Test failed - API needs further fixes")
