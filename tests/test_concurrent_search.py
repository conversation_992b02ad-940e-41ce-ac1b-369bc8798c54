#!/usr/bin/env python3
"""
Concurrent Search API Test

This script tests the /search API endpoint to verify that multiple requests
can be processed concurrently without blocking each other.

Usage:
    python test_concurrent_search.py

Requirements:
    - API server running on localhost:8000
    - Valid API key in environment variable API_KEY
    - SEERFAR credentials configured on the server
"""

import asyncio
import aiohttp
import time
import os
import json
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
API_BASE_URL = "http://localhost:8000"
API_KEY = os.getenv("API_KEY", "your-api-key-here")

# Test queries for concurrent execution
TEST_QUERIES = [
    {"query": "Find wireless headphones under $100", "test_name": "Headphones Search"},
    {"query": "Search for laptop computers with 16GB RAM", "test_name": "Laptop Search"},
    {"query": "Look for smartphone cases for iPhone", "test_name": "Phone Cases Search"},
    {"query": "Find gaming keyboards under $150", "test_name": "Gaming Keyboards Search"},
    {"query": "Search for wireless mice for office use", "test_name": "Office Mice Search"}
]

class ConcurrentSearchTester:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            headers={"X-API-Key": self.api_key},
            timeout=aiohttp.ClientTimeout(total=300)  # 5 minute timeout
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def send_search_request(self, query: str, test_name: str) -> Dict[str, Any]:
        """Send a single search request and return timing and response data"""
        start_time = time.time()
        
        try:
            logger.info(f"[{test_name}] Starting search request: {query}")
            
            # Send search request
            async with self.session.post(
                f"{self.base_url}/search",
                json={"query": query}
            ) as response:
                request_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    process_id = data.get("process_id")
                    
                    logger.info(f"[{test_name}] Request accepted in {request_time:.2f}s, process_id: {process_id}")
                    
                    return {
                        "test_name": test_name,
                        "query": query,
                        "success": True,
                        "process_id": process_id,
                        "request_time": request_time,
                        "start_time": start_time,
                        "response_data": data
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"[{test_name}] Request failed with status {response.status}: {error_text}")
                    
                    return {
                        "test_name": test_name,
                        "query": query,
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}",
                        "request_time": request_time,
                        "start_time": start_time
                    }
                    
        except Exception as e:
            request_time = time.time() - start_time
            logger.error(f"[{test_name}] Request exception: {str(e)}")
            
            return {
                "test_name": test_name,
                "query": query,
                "success": False,
                "error": str(e),
                "request_time": request_time,
                "start_time": start_time
            }
    
    async def check_process_status(self, process_id: str, test_name: str) -> Dict[str, Any]:
        """Check the status of a process"""
        try:
            async with self.session.get(f"{self.base_url}/status/{process_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "process_id": process_id,
                        "test_name": test_name,
                        "success": True,
                        "status_data": data
                    }
                else:
                    error_text = await response.text()
                    return {
                        "process_id": process_id,
                        "test_name": test_name,
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}"
                    }
        except Exception as e:
            return {
                "process_id": process_id,
                "test_name": test_name,
                "success": False,
                "error": str(e)
            }
    
    async def run_concurrent_test(self, queries: List[Dict[str, str]]) -> Dict[str, Any]:
        """Run multiple search requests concurrently and analyze results"""
        logger.info(f"Starting concurrent test with {len(queries)} requests")
        
        # Record overall test start time
        overall_start = time.time()
        
        # Create tasks for all requests
        tasks = [
            self.send_search_request(q["query"], q["test_name"]) 
            for q in queries
        ]
        
        # Execute all requests concurrently
        logger.info("Executing all requests concurrently...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        overall_time = time.time() - overall_start
        
        # Process results
        successful_requests = []
        failed_requests = []
        
        for result in results:
            if isinstance(result, Exception):
                failed_requests.append({
                    "error": str(result),
                    "exception": True
                })
            elif result.get("success"):
                successful_requests.append(result)
            else:
                failed_requests.append(result)
        
        # Calculate timing statistics
        if successful_requests:
            request_times = [r["request_time"] for r in successful_requests]
            avg_request_time = sum(request_times) / len(request_times)
            max_request_time = max(request_times)
            min_request_time = min(request_times)
        else:
            avg_request_time = max_request_time = min_request_time = 0
        
        # Check if requests were truly concurrent
        # If sequential, total time would be sum of all request times
        # If concurrent, total time should be close to the longest individual request
        sequential_time_estimate = sum(r["request_time"] for r in successful_requests) if successful_requests else 0
        concurrency_ratio = sequential_time_estimate / overall_time if overall_time > 0 else 0
        
        test_summary = {
            "total_requests": len(queries),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "overall_time": overall_time,
            "avg_request_time": avg_request_time,
            "max_request_time": max_request_time,
            "min_request_time": min_request_time,
            "sequential_time_estimate": sequential_time_estimate,
            "concurrency_ratio": concurrency_ratio,
            "is_concurrent": concurrency_ratio > 1.5,  # If ratio > 1.5, likely concurrent
            "successful_results": successful_requests,
            "failed_results": failed_requests
        }
        
        return test_summary

async def main():
    """Main test function"""
    logger.info("Starting Concurrent Search API Test")
    logger.info(f"API Base URL: {API_BASE_URL}")
    logger.info(f"Number of test queries: {len(TEST_QUERIES)}")
    
    async with ConcurrentSearchTester(API_BASE_URL, API_KEY) as tester:
        # Run the concurrent test
        results = await tester.run_concurrent_test(TEST_QUERIES)
        
        # Print detailed results
        print("\n" + "="*80)
        print("CONCURRENT SEARCH API TEST RESULTS")
        print("="*80)
        
        print(f"Total Requests: {results['total_requests']}")
        print(f"Successful Requests: {results['successful_requests']}")
        print(f"Failed Requests: {results['failed_requests']}")
        print(f"Overall Test Time: {results['overall_time']:.2f} seconds")
        print(f"Average Request Time: {results['avg_request_time']:.2f} seconds")
        print(f"Max Request Time: {results['max_request_time']:.2f} seconds")
        print(f"Min Request Time: {results['min_request_time']:.2f} seconds")
        print(f"Sequential Time Estimate: {results['sequential_time_estimate']:.2f} seconds")
        print(f"Concurrency Ratio: {results['concurrency_ratio']:.2f}")
        print(f"Concurrent Execution Detected: {'YES' if results['is_concurrent'] else 'NO'}")
        
        if results['is_concurrent']:
            print("\n✅ SUCCESS: API is processing requests concurrently!")
            speedup = results['concurrency_ratio']
            print(f"   Speedup factor: {speedup:.1f}x faster than sequential processing")
        else:
            print("\n❌ WARNING: API may be processing requests sequentially")
            print("   This suggests the concurrency fix may not be working properly")
        
        # Show individual request details
        if results['successful_results']:
            print(f"\n📋 Successful Request Details:")
            for req in results['successful_results']:
                print(f"   {req['test_name']}: {req['request_time']:.2f}s (Process: {req['process_id']})")
        
        if results['failed_results']:
            print(f"\n❌ Failed Request Details:")
            for req in results['failed_results']:
                print(f"   {req.get('test_name', 'Unknown')}: {req.get('error', 'Unknown error')}")
        
        # Save detailed results to file
        with open("concurrent_test_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to: concurrent_test_results.json")
        
        return results['is_concurrent']

if __name__ == "__main__":
    try:
        is_concurrent = asyncio.run(main())
        exit_code = 0 if is_concurrent else 1
        exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        exit(1)
