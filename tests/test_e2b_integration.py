#!/usr/bin/env python3
"""
Integration test for e2b_browser_use with new folder structure.

This test verifies that the e2b_browser_use module correctly:
1. Accepts process_id parameter
2. Creates session folders
3. Uses session-specific paths for downloads
"""

import os
import tempfile
import uuid
import sys
from unittest.mock import patch, MagicMock

# Add the current directory to Python path to import our modules
sys.path.insert(0, '/home/<USER>/10x-sales-agent')

from path_manager import get_path_manager, PathManager

def test_e2b_main_with_process_id():
    """Test that e2b_browser_use.main works with process_id parameter"""
    print("=== Testing e2b_browser_use.main with process_id ===")
    
    # Mock the e2b desktop functionality since we don't want to actually create a sandbox
    with patch('e2b_browser_use.open_desktop_stream') as mock_desktop, \
         patch('e2b_browser_use.setup_environment') as mock_setup, \
         patch('e2b_browser_use.run_bedrock_task') as mock_bedrock:
        
        # Configure mocks
        mock_desktop.return_value = MagicMock()
        mock_setup.return_value = None
        mock_bedrock.return_value = "Mock bedrock result"
        
        # Import after mocking to avoid actual e2b calls
        import e2b_browser_use
        
        # Test with process_id
        process_id = str(uuid.uuid4())
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Override the base downloads directory for testing
            original_base_dir = get_path_manager().base_downloads_dir
            get_path_manager().base_downloads_dir = temp_dir
            
            try:
                # Call main with process_id
                result = e2b_browser_use.main(
                    query="Test query", 
                    process_id=process_id
                )
                
                # Verify session folder was created
                path_manager = get_path_manager()
                session_folder = path_manager.get_session_folder()
                
                assert session_folder is not None, "Session folder should be created"
                assert os.path.exists(session_folder), "Session folder should exist on disk"
                
                # Verify folder follows naming convention
                folder_name = os.path.basename(session_folder)
                assert PathManager.is_session_folder(folder_name), "Should follow timestamp-processID pattern"
                
                # Verify process_id is stored correctly
                assert path_manager.get_process_id() == process_id, "Process ID should match"
                
                # Verify processed directory exists
                processed_path = path_manager.get_processed_path()
                assert os.path.exists(processed_path), "Processed directory should be created"
                
                print(f"✓ Session folder created: {folder_name}")
                print(f"✓ Process ID stored: {path_manager.get_process_id()}")
                print(f"✓ Downloads path: {path_manager.get_downloads_path()}")
                print(f"✓ Processed path: {processed_path}")
                print(f"✓ Function returned: {result}")
                
                # Verify mocks were called
                assert mock_desktop.called, "open_desktop_stream should be called"
                assert mock_setup.called, "setup_environment should be called"
                assert mock_bedrock.called, "run_bedrock_task should be called"
                
            finally:
                # Restore original base directory
                get_path_manager().base_downloads_dir = original_base_dir

def test_e2b_main_without_process_id():
    """Test that e2b_browser_use.main works without process_id (legacy mode)"""
    print("\n=== Testing e2b_browser_use.main without process_id (legacy mode) ===")
    
    with patch('e2b_browser_use.open_desktop_stream') as mock_desktop, \
         patch('e2b_browser_use.setup_environment') as mock_setup, \
         patch('e2b_browser_use.run_bedrock_task') as mock_bedrock:
        
        # Configure mocks
        mock_desktop.return_value = MagicMock()
        mock_setup.return_value = None
        mock_bedrock.return_value = "Mock bedrock result"
        
        # Import after mocking
        import e2b_browser_use
        
        # Reset global path manager state
        get_path_manager()._session_folder = None
        get_path_manager()._process_id = None
        
        # Call main without process_id
        result = e2b_browser_use.main(query="Test query")
        
        # Verify no session folder was created in global state
        path_manager = get_path_manager()
        assert path_manager.get_session_folder() is None, "No session folder should be created without process_id"
        
        print(f"✓ Legacy mode works without process_id")
        print(f"✓ Function returned: {result}")
        
        # Verify mocks were called
        assert mock_desktop.called, "open_desktop_stream should be called"
        assert mock_setup.called, "setup_environment should be called"
        assert mock_bedrock.called, "run_bedrock_task should be called"

def test_download_functions_with_session():
    """Test that download functions use session paths when available"""
    print("\n=== Testing download functions with session paths ===")
    
    process_id = str(uuid.uuid4())
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Set up session
        path_manager = PathManager(base_downloads_dir=temp_dir)
        session_folder = path_manager.create_session_folder(process_id)
        
        # Override global path manager for testing
        original_manager = get_path_manager()
        import e2b_browser_use
        e2b_browser_use.get_path_manager = lambda: path_manager
        
        try:
            # Mock desktop object
            mock_desktop = MagicMock()
            
            # Test download_files_from_sandbox with session
            downloaded_files = e2b_browser_use.download_files_from_sandbox(
                mock_desktop, 
                download_dir="/fake/sandbox/dir"
            )
            
            # The function should use the session downloads path
            # Since we're mocking, we can't test actual file operations,
            # but we can verify the function runs without error
            print(f"✓ download_files_from_sandbox works with session paths")
            
            # Test download_temp_files_from_multiple_dirs with session
            temp_files = e2b_browser_use.download_temp_files_from_multiple_dirs(
                mock_desktop
            )
            
            print(f"✓ download_temp_files_from_multiple_dirs works with session paths")
            
            # Test move_csv_files_to_processed with session
            e2b_browser_use.move_csv_files_to_processed()
            
            print(f"✓ move_csv_files_to_processed works with session paths")
            
        finally:
            # Restore original path manager
            e2b_browser_use.get_path_manager = lambda: original_manager

def run_integration_tests():
    """Run all integration tests"""
    print("Starting e2b_browser_use integration tests...\n")
    
    try:
        test_e2b_main_with_process_id()
        test_e2b_main_without_process_id()
        test_download_functions_with_session()
        
        print("\n" + "="*60)
        print("🎉 ALL INTEGRATION TESTS PASSED! 🎉")
        print("e2b_browser_use integration with new folder structure works correctly.")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ INTEGRATION TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = run_integration_tests()
    exit(0 if success else 1)
