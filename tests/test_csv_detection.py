#!/usr/bin/env python3
"""
Test script to verify CSV file detection logic
"""

import os
import glob
from datetime import datetime, timezone

def test_csv_detection():
    """Test the CSV file detection logic from app.py"""
    downloads_path = "/home/<USER>/10x-sales-agent/downloads"
    
    print(f"Testing CSV file detection in: {downloads_path}")
    
    # Test 1: Look for any CSV files (new logic)
    print("\n=== Test 1: New Logic (any CSV files) ===")
    csv_files = []
    for file in glob.glob(os.path.join(downloads_path, "*.csv")):
        # Skip files in processed subdirectory
        if 'processed' not in file:
            csv_files.append(file)
    
    print(f"Found {len(csv_files)} CSV files:")
    for file in csv_files:
        file_size = os.path.getsize(file)
        file_time = datetime.fromtimestamp(os.path.getctime(file), timezone.utc)
        print(f"  - {os.path.basename(file)}")
        print(f"    Size: {file_size:,} bytes")
        print(f"    Created: {file_time}")
    
    if csv_files:
        latest_file = max(csv_files, key=os.path.getctime)
        print(f"\n✅ Latest file: {os.path.basename(latest_file)}")
    else:
        print(f"\n❌ No CSV files found")
    
    # Test 2: Old logic (Seerfar-Product pattern)
    print("\n=== Test 2: Old Logic (Seerfar-Product pattern) ===")
    old_pattern_files = glob.glob(os.path.join(downloads_path, "Seerfar-Product*.csv"))
    print(f"Found {len(old_pattern_files)} files matching 'Seerfar-Product*.csv':")
    for file in old_pattern_files:
        print(f"  - {os.path.basename(file)}")
    
    # Test 3: Verify file content
    if csv_files:
        test_file = csv_files[0]
        print(f"\n=== Test 3: File Content Verification ===")
        print(f"Testing file: {os.path.basename(test_file)}")
        
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                line_count = sum(1 for _ in f) + 1
            
            print(f"✅ File is readable")
            print(f"✅ Line count: {line_count:,}")
            print(f"✅ Header: {first_line[:100]}...")
            
            # Check if it looks like Seerfar data
            if any(keyword in first_line.lower() for keyword in ['no.', 'title', 'sku', 'price', 'sales']):
                print(f"✅ File appears to contain Seerfar product data")
            else:
                print(f"⚠️  File may not contain expected Seerfar data")
                
        except Exception as e:
            print(f"❌ Error reading file: {e}")

if __name__ == "__main__":
    test_csv_detection()
