#!/usr/bin/env python3
"""
Test script to manually run post-processing on existing downloaded files
"""

import os
import sys
import logging
import glob

# Add the current directory to Python path to import our module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the post-processing function from our main module
from e2b_browser_use import process_downloaded_files

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Test the post-processing functionality on existing files"""
    downloads_dir = "/home/<USER>/10x-sales-agent/downloads"
    
    print(f"Checking for files in: {downloads_dir}")
    
    if not os.path.exists(downloads_dir):
        print(f"Downloads directory does not exist: {downloads_dir}")
        return
    
    # Find all files in downloads directory, excluding the processed subdirectory
    pattern = os.path.join(downloads_dir, "*")
    all_items = glob.glob(pattern)
    existing_files = [f for f in all_items if os.path.isfile(f) and not f.endswith('.csv')]
    
    print(f"Found {len(existing_files)} files to process:")
    for file_path in existing_files:
        print(f"  - {file_path}")
    
    if not existing_files:
        print("No files found to process")
        return
    
    # Run post-processing
    print("\nStarting post-processing...")
    processed_files = process_downloaded_files(existing_files)
    
    print(f"\nPost-processing complete!")
    print(f"Processed {len(processed_files)} files:")
    for file_path in processed_files:
        print(f"  - {file_path}")

if __name__ == "__main__":
    main()
