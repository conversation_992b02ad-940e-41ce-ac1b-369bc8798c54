{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"id": "ZRGlUb8O4fPV"}, "outputs": [], "source": ["%pip install -U langgraph langchain_google_genai langchain_community langgraph-checkpoint-postgres  openai langchain_groq"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "cMfPUmHIxqTi"}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --upgrade --quiet  playwright > /dev/null\n", "%pip install --upgrade --quiet  lxml browser-use langchain_openai"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kkZ7jVUOUV7Q"}, "outputs": [], "source": ["!playwright install"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-_T1MhnGUl2q"}, "outputs": [], "source": ["!pip install \"anyio<4\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yARYirp1UhDR"}, "outputs": [], "source": ["# This import is required only for jupyter notebooks, since they have their own eventloop\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "jyVP10O_5Qck"}, "outputs": [], "source": ["from google.colab import userdata\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model='gpt-4o-mini', temperature=0, api_key=userdata.get('Open_api_key'))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "e9duizdv5cOH", "outputId": "a07b1702-d485-4641-c307-601e6ab34b9b"}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 10, 'prompt_tokens': 8, 'total_tokens': 18, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_bd83329f63', 'finish_reason': 'stop', 'logprobs': None}, id='run-28a9088f-7539-412a-aa80-1663be40e74f-0', usage_metadata={'input_tokens': 8, 'output_tokens': 10, 'total_tokens': 18, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.invoke('hi')"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wS8ouhiVQ2dL", "outputId": "653879a8-b3ac-4178-edee-5cd834e3404a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍  Searched for \"What is <PERSON><PERSON>?\" in Google\n", "\n", "\n", "\n", "\n", "\n", "📄  Extracted page as markdown\n", ": ![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac879f622b3cb30dd7_cohere-logos-\n", "idbbhgStc3%201.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdacfdbb3072f5258f66_hugging%20face.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdaceb29ce1602beb431_logo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac5f6f2a8c34e5575b_wblogo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdade49955197d2a8941_mosaic.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac5092327565075208_aws.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdacb28fe27c7784c797_goggle%20drive.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac325d487977a3398b_milvus.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac6348e83137a80c17_openai.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac0d888384ad7d31f3_redis.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdacf9d2dfca1d2a4c81_google%20cloud.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac76b6b8b79414144f_datastax%20logo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac15e6989ae752a9b5_notion%20logo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac485cb9900ddafda3_anthropic-\n", "logo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdade49955197d2a894d_mongodb.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdacaeab9fdc6452063c_supabase.png)\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac879f622b3cb30dd7_cohere-logos-\n", "idbbhgStc3%201.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdacfdbb3072f5258f66_hugging%20face.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdaceb29ce1602beb431_logo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac5f6f2a8c34e5575b_wblogo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdade49955197d2a8941_mosaic.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac5092327565075208_aws.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdacb28fe27c7784c797_goggle%20drive.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac325d487977a3398b_milvus.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac6348e83137a80c17_openai.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac0d888384ad7d31f3_redis.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdacf9d2dfca1d2a4c81_google%20cloud.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac76b6b8b79414144f_datastax%20logo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac15e6989ae752a9b5_notion%20logo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdac485cb9900ddafda3_anthropic-\n", "logo.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdade49955197d2a894d_mongodb.png)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c8fdacaeab9fdc6452063c_supabase.png)\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667b080e4b3ca12dc5d5d439_Langgraph%20UI-2.webp)\n", "\n", "## Controllable cognitive architecture for any task\n", "\n", "LangGraph's flexible framework supports diverse control flows – single agent,\n", "multi-agent, hierarchical, sequential – and robustly handles realistic,\n", "complex scenarios.  \n", "  \n", "Ensure reliability with easy-to-add moderation and quality loops that prevent\n", "agents from veering off course.  \n", "  \n", "Use LangGraph Platform to templatize your cognitive architecture so that\n", "tools, prompts, and models are easily configurable with LangGraph Platform\n", "Assistants.\n", "\n", "[See the docs ](https://langchain-ai.github.io/langgraph/)\n", "\n", "## Designed for human-agent collaboration\n", "\n", "With built-in statefulness, LangGraph agents seamlessly collaborate with\n", "humans by writing drafts for review and awaiting approval before acting.\n", "Easily inspect the agent’s actions and \"time-travel\" to roll back and take a\n", "different action to correct course.\n", "\n", "[Read a conceptual guide ](https://langchain-\n", "ai.github.io/langgraph/concepts/agentic_concepts/#human-in-the-loop)\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667c93d559216bb904fe85a8_gif7%20\\(1\\).gif)\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667c57f274b66a77e2a26b82_CleanShot2024-06-26at17.08.03-ezgif.com-\n", "video-to-gif-converter.gif)\n", "\n", "## First class streaming support for better UX design\n", "\n", "Bridge user expectations and agent capabilities with native token-by-token\n", "streaming and streaming of intermediate steps, helpful for showing agent\n", "reasoning and actions back to the user as they happen. Use LangGraph\n", "Platform's API to deliver dynamic and interactive user experiences.\n", "\n", "[Learn more ](https://langchain-ai.github.io/langgraph/how-tos/streaming-\n", "tokens/)\n", "\n", "## Why choose <PERSON><PERSON><PERSON><PERSON>?\n", "\n", "### Control, moderate, and guide your agent’s actions.\n", "\n", "Prevent agents from veering off course and ensure reliability with easy-to-add\n", "moderation and quality loops. Add human-in-the-loop to steer and approve agent\n", "actions.\n", "\n", "### Expressive and customizable agent and multi-agent workflows.\n", "\n", "LangGraph’s low level abstractions offer the flexibility needed to create\n", "sophisticated agents. Design diverse control flows – single, multi-agent,\n", "hierarchical, sequential – all with one framework.\n", "\n", "### Persisted context for long-term interactions.\n", "\n", "With its stateful design, LangGraph stores conversation histories and session\n", "data to maintain context over time and ensure smooth handoffs in agentic\n", "systems.\n", "\n", "### First-class streaming support for better UX design.\n", "\n", "Bridge user expectations and agent capabilities with native token-by-token\n", "streaming of intermediate steps, helpful for showing agent reasoning and\n", "actions back to the user as they happen.\n", "\n", "## LangGraph Platform:  \n", "Deploy & develop agents at scale\n", "\n", "Craft agent-appropriate UXs using LangGraph Platform's APIs. Quickly deploy\n", "and scale your agent with purpose-built infrastructure. Choose from multiple\n", "deployment options.\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/67878de387cf10f90c7ad65f_LangGraph---\n", "Memory-HQ.gif)\n", "\n", "## Dynamic APIs for designing agent UXs.\n", "\n", "Craft personalized experiences with the long-term memory API to recall\n", "information across conversation sessions. Expose, update, and rewind your\n", "app's state for better user visibility, steering, and interaction. Kick off\n", "long-running background jobs for research-style or multi-step work.\n", "\n", "[See the docs ](https://langchain-ai.github.io/langgraph/how-tos/streaming-\n", "tokens/)\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/67879a0dd9100d8e643eb39e_LangGraph%20-%20Fault-\n", "tolerant%20scalability.gif)\n", "\n", "## Fault-tolerant scalability.\n", "\n", "Handle large workloads gracefully with horizontally-scaling servers, task\n", "queues, and built-in persistence. Enhance resilience with intelligent caching\n", "and automated retries.\n", "\n", "[Learn more in the blog ](https://langchain-ai.github.io/langgraph/how-\n", "tos/streaming-tokens/)\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667c93d559216bb904fe85a8_gif7%20\\(1\\).gif)\n", "\n", "## An end-to-end agent experience.\n", "\n", "Simplify prototyping, debugging, and sharing of agents in our visual LangGraph\n", "Studio. Deploy your application with 1-click deploy with our SaaS offering or\n", "within your own VPC. Then, monitor app performance with LangSmith.\n", "\n", "[Discover LangGraph Studio ](https://langchain-ai.github.io/langgraph/how-\n", "tos/streaming-tokens/)\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/66db8c2317fe5b9ad2b84ea0_lcacademylogo.png)\n", "\n", "## Introduction to <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Learn the basics of LangGraph in this LangChain Academy Course. You'll learn\n", "how to build agents that automate real-world tasks with LangGraph\n", "orchestration.\n", "\n", "[Enroll for free](https://academy.langchain.com/courses/intro-to-\n", "langgraph)[Book enterprise\n", "training](https://airtable.com/appGjCAN6126Jm7K8/pagNAp7niHQzRH8zk/form)\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/6787ae429071ad3575902249_card%201%201.webp)![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/6787ae0bce5c99dd808545ce_card%202.webp)\n", "\n", "## Deploy agents at scale, monitor carefully, iterate boldly\n", "\n", "Design agent-driven user experiences with LangGraph Platform's APIs. Quickly\n", "deploy and scale your application with infrastructure built for agents. Choose\n", "from multiple deployment options.\n", "\n", "### Fault-tolerant scalability\n", "\n", "Handle large workloads gracefully with horizontally-scaling servers, task\n", "queues, and built-in persistence. Enhance resilience with intelligent caching\n", "and automated retries.\n", "\n", "### Dynamic APIs for designing agent experience\n", "\n", "Craft personalized user experiences with APIs featuring long-term memory to\n", "recall information across conversation sessions. Track, update, and rewind\n", "your app's state for easy human steering and interaction. Kick off long-\n", "running background jobs for research-style or multi-step work.\n", "\n", "### Integrated developer experience\n", "\n", "Simplify prototyping, debugging, and sharing of agents in our visual LangGraph\n", "Studio. Deploy your application with 1-click deploy with our SaaS offering or\n", "within your own VPC. Then, monitor app performance with LangSmith.\n", "\n", "### Trusted by companies taking agency in AI innovation:\n", "\n", "LangGraph helps teams of all sizes, across all industries, from ambitious\n", "startups to established enterprises.\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c5308aea1371b447cc4af9_elastic-ar21.png)\n", "\n", "“Lang<PERSON>hai<PERSON> is streets ahead with what they've put forward with LangGraph.\n", "LangGraph sets the foundation for how we can build and scale AI workloads —\n", "from conversational agents, complex task automation, to custom LLM-backed\n", "experiences that 'just work'. The next chapter in building complex production-\n", "ready features with LLMs is agentic, and with LangGraph and LangSmith,\n", "<PERSON><PERSON><PERSON><PERSON> delivers an out-of-the-box solution to iterate quickly, debug\n", "immediately, and scale effortlessly.”\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667b26a1b4576291d6a9335b_garrett%20spong%201.webp)\n", "\n", "<PERSON>\n", "\n", "Principal <PERSON><PERSON><PERSON>\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/6679de9dc4e7bee218d4b058_Norwegian-Cruise-\n", "Line-Logo%202-2.webp)\n", "\n", "“LangGraph has been instrumental for our AI development. Its robust framework\n", "for building stateful, multi-actor applications with LLMs has transformed how\n", "we evaluate and optimize the performance of our AI guest-facing solutions.\n", "LangGraph enables granular control over the agent's thought process, which has\n", "empowered us to make data-driven and deliberate decisions to meet the diverse\n", "needs of our guests.”\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667b265bed5f5a9d26d6b7d6_andres%20torres%201.webp)\n", "\n", "<PERSON><PERSON>\n", "\n", "Sr. Solutions Architect\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667c6f809f0ebc7b1d72a99b_Replit.png)\n", "\n", "“It's easy to build the prototype of a coding agent, but deceptively hard to\n", "improve its reliability. Replit wants to give a coding agent to millions of\n", "users — reliability is our top priority, and will remain so for a long time.\n", "LangGraph is giving us the control and ergonomics we need to build and ship\n", "powerful coding agents.”\n", "\n", "“As Ally advances its exploration of Generative AI,\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667c6fcaaa21bcf2fe006dbe_1690576438641%20\\(1\\)%201.webp)\n", "\n", "<PERSON>\n", "\n", "President\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/6679e1baf7ea357d0763cde1_ally-\n", "bank%201-2.png)\n", "\n", "“As Ally advances its exploration of Generative AI, our tech labs is excited\n", "by LangGraph, the new library from LangChain, which is central to our\n", "experiments with multi-actor agentic workflows. We are committed to deepening\n", "our partnership with <PERSON><PERSON><PERSON><PERSON>.”\n", "\n", "“As Ally advances its exploration of Generative AI,\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/6679e2d31352c6bd56c84280_ally.png)\n", "\n", "<PERSON><PERSON><PERSON>\n", "\n", "Chief Information, Data and Digital Officer\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/65c5308aea1371b447cc4af9_elastic-ar21.png)\n", "\n", "“Lang<PERSON>hai<PERSON> is streets ahead with what they've put forward with LangGraph.\n", "LangGraph sets the foundation for how we can build and scale AI workloads —\n", "from conversational agents, complex task automation, to custom LLM-backed\n", "experiences that 'just work'. The next chapter in building complex production-\n", "ready features with LLMs is agentic, and with LangGraph and LangSmith,\n", "<PERSON><PERSON><PERSON><PERSON> delivers an out-of-the-box solution to iterate quickly, debug\n", "immediately, and scale effortlessly.”\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667b26a1b4576291d6a9335b_garrett%20spong%201.webp)\n", "\n", "<PERSON>\n", "\n", "Principal <PERSON><PERSON><PERSON>\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/6679de9dc4e7bee218d4b058_Norwegian-Cruise-\n", "Line-Logo%202-2.webp)\n", "\n", "“LangGraph has been instrumental for our AI development. Its robust framework\n", "for building stateful, multi-actor applications with LLMs has transformed how\n", "we evaluate and optimize the performance of our AI guest-facing solutions.\n", "LangGraph enables granular control over the agent's thought process, which has\n", "empowered us to make data-driven and deliberate decisions to meet the diverse\n", "needs of our guests.”\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667b265bed5f5a9d26d6b7d6_andres%20torres%201.webp)\n", "\n", "<PERSON><PERSON>\n", "\n", "Sr. Solutions Architect\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667c6f809f0ebc7b1d72a99b_Replit.png)\n", "\n", "“It's easy to build the prototype of a coding agent, but deceptively hard to\n", "improve its reliability. Replit wants to give a coding agent to millions of\n", "users — reliability is our top priority, and will remain so for a long time.\n", "LangGraph is giving us the control and ergonomics we need to build and ship\n", "powerful coding agents.”\n", "\n", "“As Ally advances its exploration of Generative AI,\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/667c6fcaaa21bcf2fe006dbe_1690576438641%20\\(1\\)%201.webp)\n", "\n", "<PERSON>\n", "\n", "President\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/6679e1baf7ea357d0763cde1_ally-\n", "bank%201-2.png)\n", "\n", "“As Ally advances its exploration of Generative AI, our tech labs is excited\n", "by LangGraph, the new library from LangChain, which is central to our\n", "experiments with multi-actor agentic workflows. We are committed to deepening\n", "our partnership with <PERSON><PERSON><PERSON><PERSON>.”\n", "\n", "“As Ally advances its exploration of Generative AI,\n", "\n", "![](https://cdn.prod.website-\n", "files.com/65b8cd72835ceeacd4449a53/6679e2d31352c6bd56c84280_ally.png)\n", "\n", "<PERSON><PERSON><PERSON>\n", "\n", "Chief Information, Data and Digital Officer\n", "\n", "## LangGraph FAQs\n", "\n", "Do I need to use LangChain to use LangGraph? What’s the difference?\n", "\n", "No. LangGraph is an orchestration framework for complex agentic systems and is\n", "more low-level and controllable than LangChain agents. LangChain provides a\n", "standard interface to interact with models and other components, useful for\n", "straight-forward chains and retrieval flows.\n", "\n", "How is LangGraph different from other agent frameworks?\n", "\n", "Other agentic frameworks can work for simple, generic tasks but fall short for\n", "complex tasks bespoke to a company’s needs. LangGraph provides a more\n", "expressive framework to handle companies’ unique tasks without restricting\n", "users to a single black-box cognitive architecture.\n", "\n", "Does LangGraph impact the performance of my app?\n", "\n", "LangGraph will not add any overhead to your code and is specifically designed\n", "with streaming workflows in mind.\n", "\n", "Is LangGraph open source? Is it free?\n", "\n", "Yes. LangGraph is an MIT-licensed open-source library and is free to use.\n", "\n", "How are LangGraph and LangGraph Platform different?\n", "\n", "LangGraph is a stateful, orchestration framework that brings added control to\n", "agent workflows. LangGraph Platform is a service for deploying and scaling\n", "LangGraph applications, with an opinionated API for building agent UXs, plus\n", "an integrated developer studio.\n", "\n", "LangGraph (open source)\n", "\n", "LangGraph Platform\n", "\n", "Features\n", "\n", "Stateful orchestration framework for agentic applications\n", "\n", "Scalable infrastructure for deploying LangGraph applications  \n", "\n", "Python and JavaScript\n", "\n", "Python and JavaScript  \n", "\n", "None\n", "\n", "Yes - useful for retrieving & updating state or long-term memory, or creating\n", "a configurable assistant  \n", "\n", "Basic\n", "\n", "Dedicated mode for token-by-token messages  \n", "\n", "Community contributed\n", "\n", "Supported out-of-the-box  \n", "\n", "Self-managed\n", "\n", "Managed Postgres with efficient storage  \n", "\n", "Self-managed\n", "\n", "\\- Cloud SaaS  \n", "\\- Free self-hosted  \n", "\\- Enterprise  \n", "(BYOC or paid self-hosted)  \n", "\n", "Self-managed\n", "\n", "Auto-scaling of task queues and servers  \n", "\n", "Self-managed\n", "\n", "Automated retries  \n", "\n", "Simple threading\n", "\n", "Supports double-texting  \n", "\n", "None\n", "\n", "Cron scheduling  \n", "\n", "None\n", "\n", "Integrated with LangSmith for observability  \n", "\n", "LangGraph Studio for Desktop\n", "\n", "LangGraph Studio for Desktop & Cloud  \n", "\n", "What are my deployment options for LangGraph Platform?\n", "\n", "We currently have the following deployment options for LangGraph applications:  \n", "  \n", "‍**Self-Hosted Lite** : A free (up to 1M nodes executed), limited version of\n", "LangGraph Platform that you can run locally or in a self-hosted manner. This\n", "version requires a LangSmith API key and logs all usage to LangSmith. Fewer\n", "features are available than in paid plans.  \n", "‍**Cloud SaaS:** <PERSON><PERSON> managed and hosted as part of LangSmith, with automatic\n", "updates and zero maintenance.  \n", "‍**Bring Your Own Cloud (BYOC):** Deploy LangGraph Platform within your VPC,\n", "provisioned and run as a service. Keep data in your environment while\n", "outsourcing the management of the service.  \n", "**Self-Hosted Enterprise:** Deploy LangGraph entirely on your own\n", "infrastructure.\n", "\n", "Is LangGraph Platform open source?\n", "\n", "No. LangGraph Platform is proprietary software.  \n", "  \n", "There is a free, self-hosted version of LangGraph Platform with access to\n", "basic features. The Cloud SaaS deployment option is free while in beta, but\n", "will eventually be a paid service. We will always give ample notice before\n", "charging for a service and reward our early adopters with preferential\n", "pricing. The Bring Your Own Cloud (BYOC) and Self-Hosted Enterprise options\n", "are also paid services. [Contact our sales team](/contact-sales) to learn\n", "more.  \n", "  \n", "For more information, see our [LangGraph Platform pricing page](/pricing-\n", "langgraph-platform).\n", "\n", "## Ready to start shipping reliable GenAI apps faster?\n", "\n", "Get started with Lang<PERSON><PERSON><PERSON>, LangSmith, and LangGraph to enhance your LLM app\n", "development, from prototype to production.\n", "\n", "[Contact Us](/contact-sales)[Sign Up](https://smith.langchain.com/)\n", "\n", "\n", "\n", "\n", "\n", "LangGraph is a flexible framework designed for building and scaling agentic applications. It allows for complex task handling and human-agent collaboration, supporting various control flows such as single-agent, multi-agent, hierarchical, and sequential. Key features include:\n", "\n", "- **Statefulness**: LangGraph agents maintain context over time, enabling smooth interactions.\n", "- **Streaming Support**: It provides native token-by-token streaming for better user experience.\n", "- **Moderation and Quality Loops**: These features ensure agents remain reliable and on course.\n", "- **Dynamic APIs**: LangGraph offers APIs for crafting personalized user experiences and managing long-term memory.\n", "- **Deployment Options**: It supports various deployment methods, including self-hosted and cloud solutions.\n", "\n", "\n", "\n", "\n"]}], "source": ["import asyncio\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "from browser_use import Agent, Browser, BrowserConfig\n", "\n", "# Basic configuration for the browser\n", "config = BrowserConfig(\n", "\theadless=True,  # Run in headless mode\n", "\t# disable_security=True  # Uncomment if you want to disable security\n", ")\n", "\n", "# Initialize the browser with the specified configuration\n", "browser = Browser(config=config)\n", "\n", "\n", "async def main():\n", "\t# Initialize the agent with the task and language model\n", "\tagent = Agent(\n", "\t\ttask='What is <PERSON><PERSON>',\n", "\t\tllm=llm,  # Replace with your LLM configuration\n", "\t\tbrowser=browser,\n", "\t\tgenerate_gif=False,  # Disable GIF generation\n", "\t)\n", "\n", "\t# Run the agent and get results asynchronously\n", "\tresult = await agent.run()\n", "\n", "\t# Process results token-wise\n", "\tfor action in result.action_results():\n", "\t\tprint(action.extracted_content, end='\\r', flush=True)\n", "\t\tprint('\\n\\n')\n", "\t\t# if action.is_done:\n", "\t\t#     print(action.extracted_content)\n", "\n", "\t# Close the browser after completion\n", "\tawait browser.close()\n", "\n", "\n", "# Run the asynchronous main function\n", "asyncio.run(main())"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TFK-fNoLDFcF", "outputId": "d78fbeae-c8f0-4c26-e0e3-7a0a683d3fc1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AgentHistoryList(all_results=[ActionResult(is_done=False, extracted_content='🔍  Searched for \"What is <PERSON><PERSON>hai<PERSON>?\" in Google', error=None, include_in_memory=True), ActionResult(is_done=False, extracted_content=\"📄  Extracted page as markdown\\n: # Filters and Topics\\n\\n[All](/search?sca_esv=4c6b8dc13bab3e46&q=What+is+LangChain%3F&source=lnms&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ0pQJegQIEhAB)\\n\\n[Images](/search?sca_esv=4c6b8dc13bab3e46&q=What+is+LangChain%3F&udm=2&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQtKgLegQIExAB)\\n\\n[Videos](/search?sca_esv=4c6b8dc13bab3e46&q=What+is+LangChain%3F&udm=7&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQtKgLegQIERAB)\\n\\n[Forums](/search?sca_esv=4c6b8dc13bab3e46&q=What+is+LangChain%3F&udm=18&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQs6gLegQIDxAB)\\n\\nWeb\\n\\n[Flights](/travel/flights?sca_esv=4c6b8dc13bab3e46&output=search&q=What+is+LangChain%3F&source=lnms&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&ved=1t:200715&ictx=111)\\n\\n[Finance](/finance?sca_esv=4c6b8dc13bab3e46&output=search&q=What+is+LangChain%3F&source=lnms&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ0pQJegQIDBAB)\\n\\nMore\\n\\n[Books](/search?sca_esv=4c6b8dc13bab3e46&q=What+is+LangChain%3F&udm=36&source=lnms&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ0pQJegQINxAB)\\n\\n[News](/search?sca_esv=4c6b8dc13bab3e46&q=What+is+LangChain%3F&tbm=nws&source=lnms&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ0pQJegQINhAB)\\n\\n[Shopping](/search?sca_esv=4c6b8dc13bab3e46&q=What+is+LangChain%3F&udm=28&fbs=AEQNm0Aa4sjWe7Rqy32pFwRj0UkWd8nbOJfsBGGB5IQQO6L3JyWp6w6_rxLPe8F8fpm5a55blYtaduielx1say4YCS0EIyvBb6VkaLhDZSOnSC94tp-\\nJuFEDkvqUl_u6quB-Is11hrT6R6Y6jGPIGI0MqGRIdRYfHHK4Fm5f9UNWxYphEnPjChpmH-\\nusjmkJN6Sk444PHRuqJvihdKgoqwGrUjYjqVvmxA&ved=1t:220175&ictx=111)\\n\\nTools\\n\\nAny time\\n\\nAny time\\n\\n[Past\\nhour](/search?q=What+is+LangChain%3F&sca_esv=4c6b8dc13bab3e46&udm=14&source=lnt&tbs=qdr:h&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQpwV6BAgGEAc)\\n\\n[Past 24\\nhours](/search?q=What+is+LangChain%3F&sca_esv=4c6b8dc13bab3e46&udm=14&source=lnt&tbs=qdr:d&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQpwV6BAgGEAg)\\n\\n[Past\\nweek](/search?q=What+is+LangChain%3F&sca_esv=4c6b8dc13bab3e46&udm=14&source=lnt&tbs=qdr:w&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQpwV6BAgGEAk)\\n\\n[Past\\nmonth](/search?q=What+is+LangChain%3F&sca_esv=4c6b8dc13bab3e46&udm=14&source=lnt&tbs=qdr:m&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQpwV6BAgGEAo)\\n\\n[Past\\nyear](/search?q=What+is+LangChain%3F&sca_esv=4c6b8dc13bab3e46&udm=14&source=lnt&tbs=qdr:y&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQpwV6BAgGEAs)\\n\\nCustom range...\\n\\nCustom date range\\n\\nFromTo\\n\\nGo\\n\\nAll results\\n\\nAll results\\n\\n[Verbatim](/search?q=What+is+LangChain%3F&sca_esv=4c6b8dc13bab3e46&udm=14&source=lnt&tbs=li:1&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQpwV6BAgGEBM)\\n\\n[ Advanced Search\\n](https://www.google.com/advanced_search?q=What+is+LangChain%3F&udm=14)\\n\\nCtrl+Shift+X to select\\n\\n![Google](https://fonts.gstatic.com/s/i/productlogos/googleg/v6/24px.svg)\\n\\n# Search settings\\n\\n[Search CustomizationOff](/history/optout?hl=en)\\n\\n[SafeSearchBlurring\\non](/safesearch?prev=https://www.google.com/search?q%3DWhat%2Bis%2BLangChain?%26udm%3D14&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8JsIegQIChAH)\\n\\n[LanguageEnglish](/preferences?lang=1&hl=en&prev=https://www.google.com/search?q%3DWhat%2Bis%2BLangChain%253F%26sca_esv%3D4c6b8dc13bab3e46%26udm%3D14#languages)\\n\\n[Dark themeDevice\\ndefault](/setprefs?hl=en&prev=https://www.google.com/search?q%3DWhat%2Bis%2BLangChain?%26udm%3D14%26pccc%3D1&sig=0_jfSkJcafppJyKAIkCWZpHFXzfrs%3D&cs=2&sa=X&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQqsEHegQIChAJ&ictx=1)\\n\\n[More\\nsettings](/preferences?hl=en&prev=https://www.google.com/search?q%3DWhat%2Bis%2BLangChain%253F%26sca_esv%3D4c6b8dc13bab3e46%26udm%3D14)\\n\\nSend feedback\\n\\n[Help](https://support.google.com/websearch/?p=dsrp_search_hc&hl=en) •\\n[Privacy](https://policies.google.com/privacy?hl=en&fg=1) •\\n[Terms](https://policies.google.com/terms?hl=en&fg=1)\\n\\n# Search Results\\n\\n[  \\nLangChain![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAM1BMVEUcPDwRNjYAMC8AKSd2goaZoaapr7T//v/g4ej49/+/xMn8+/8AFRNAVliSm6BUZWfLztSDUJcgAAAAu0lEQVR4AdWRR2JFIQhFLcgF+/5XG54lPZn/M+Qo1b0iPnzBf1LRU/oC+fjuGD/gY4NANUvRSwEUEta/DAXVKtchxSaKbH99gwWaC4Tzrw/NFkTzLvCTDxxiXxbcJlChhYOL85FlRhcTzJEnJ9SxQkuatQpVSkkE3ytBlwy8pdUPA2gCbWxupV0NGRhuVEEnGad483sUgynlScV6Xf/WKHcJhmh5SqEsJ+Hz+iz6Y31n8f0L5ON/J3tB3gAtjgsX/sngiAAAAABJRU5ErkJggg==)LangChainhttps://www.langchain.com](https://www.langchain.com/)\\n\\nLangChain\\n\\nhttps://www.langchain.com\\n\\n _LangChain_ is a composable framework to build with LLMs. LangGraph is the\\norchestration framework for controllable agentic workflows. Run.\\n\\n\\u200e[Docs](https://python.langchain.com/docs/introduction/) ·\\n\\u200e[Products](https://www.langchain.com/langchain) · \\u200e[LangChain\\nAcademy](https://academy.langchain.com/) · \\u200e[Join the LangChain\\nCommunity](https://www.langchain.com/join-community)\\n\\n[  \\nWhat is\\nLangChain?![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAflBMVEUjLz4dKjohLT0NHzFQV2FKUlwAFywnM0IaKDgzPUpWXGUVJDbq6+3i4+X29/jLzc99gogAABubnqP///9yd393fIPY2twAAAAAAB8AACK1t7ujpqsADicAFitiaHGGi5GUmJ1pb3cAFCqJjpQ8RlIuOUZDS1errrEGHC/DxslAWrmhAAAA1UlEQVR4Ad2OhWGFMBBAI0iIlhzuTth/wHqLjPBf5FzQ64Hx10++H8H3GPX8IMQEE8JCGnFC0ImQSps3GVuIE5lCpii6EOQFhFAaHVV1ZvPm1rWSGbSqk3UvvQ70cKlkI8QFUGtMZ3QzxRz4uRPmMBvoFrAlVEVlB4jIpW1S8W6l/SLSjfF93xw6IZPDDCFBvi52Sd2zs+1haSB+OxHhzz2Is3KycKRomtp2mthYyTFr0YlbKwCtTJZp0LWbO4YuEBd09WHMYXlDCWPoAaMuCBzF6BX5AC2JD1u/hbEIAAAAAElFTkSuQmCC)Amazon\\nWeb Serviceshttps://aws.amazon.com › ... › Generative\\nAI](https://aws.amazon.com/what-is/langchain/)\\n\\nAmazon Web Services\\n\\nhttps://aws.amazon.com › ... › Generative AI\\n\\nLangChain _provides AI developers with tools to connect language models with\\nexternal data sources_. It is open-source and supported by an active\\ncommunity.\\n\\n[  \\nWhat Is LangChain and How to Use It: A\\nGuide![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAANlBMVEVHcEwAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkT2h/2dAAAAEnRSTlMASA176IbPqP9pXzX1LR7fI79igdKzAAAA60lEQVR4Ab2SR2IDMQgAR7BoEYuK///YVHf7msxJojf+g1J4i+hm1Erd3/hsvhVEaCH7wQPh2YAeB4wM7ik+F+uEuacC7c5XMocUCWCYVyHtpjQPSoW278GYFeHGNllCn1W1zjVcaSfOHG7UYBqATSzvlOEFodXzj+V39aivbuzKDz3I4FRuyvCbspCxXG9hDx9xH7Z4nJXdjbRzQdKwxLzftaI+1qzai7FcmdtdRY06B20vsGalud7Gt+WQ6jZgmVdZucnT4DU901NZ08vryo6IA1p6vCx7Wlmr2M/WX8/Ef9hUeEMP1ej8OZ+MHAj3YNWlQgAAAABJRU5ErkJggg==)TechTargethttps://www.techtarget.com\\n› definition ›\\nLangChain](https://www.techtarget.com/searchenterpriseai/definition/LangChain)\\n\\nTechTarget\\n\\nhttps://www.techtarget.com › definition › LangChain\\n\\n _LangChain is an open source framework_ that enables software developers\\nworking with artificial intelligence (AI) and its machine learning subset to\\ncombine ...\\n\\n[  \\nIntroduction | 🦜️ LangChain![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAPFBMVEUdPT1OZGZzg4fT194cPDwUNzf///8dPT0ePj75+P/y8vrAxcw6UlQGMjGSnqMsSEnk5u2Cj5OrtbpgdHaG8/c5AAAACXRSTlPv////////b24kxPwmAAAA1klEQVQokcWS2Y7DIAxFsR3TYhaz/P+/DkvSppFSaR5Gcx+Q4HjjgnludzJPY25hx1/YX0P+0Bkya4CTgm58QFYk+yEqyguyVmfJZ3coZysp8MpM4nKIfV3ypdROZyYD9eCiwe8MPYFYAu4w4kjJLS7qoQdv4gTjgMX2M0mRlSaDFqp1tiw4q5FybCJAhFpH+ITcaPXaQiTpDXGWXz37tGMjtaWSrEesMtvsJoQ6JvKeJI9Lzjr1uCeHdHVoerB7q9DwpAZvb69v8nqW//wmv4bGPO7x4weTRBHU/VcIdwAAAABJRU5ErkJggg==)LangChainhttps://python.langchain.com › docs › introduction](https://python.langchain.com/docs/introduction/)\\n\\nLangChain\\n\\nhttps://python.langchain.com › docs › introduction\\n\\n _LangChain_ is a framework for developing applications powered by large\\nlanguage models (LLMs). LangChain simplifies every stage of the LLM\\napplication lifecycle.\\n\\n\\u200e[Introduction](https://python.langchain.com/v0.1/docs/get_started/introduction/)\\n·\\n\\u200e[Langchain.agents...](https://api.python.langchain.com/en/latest/agents/langchain.agents.tool_calling_agent.base.create_tool_calling_agent.html)\\n· \\u200e[LangChain v0.3](https://python.langchain.com/docs/versions/v0_3/) ·\\n\\u200e[Langchain_core.tools.](https://api.python.langchain.com/en/latest/tools/langchain_core.tools.tool.html)\\n\\n[  \\nWhat Is\\nLangChain?![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAQlBMVEVHcEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABz07T7AAAAFnRSTlMABWTNoAuOPcGA32tTRXW1FyYt7PT+Xc8YuAAAANZJREFUeAHNx8t1xSAMBcArQCD+AkP/rcYhXiTHKeDNbvC5yFjH5K0hvAWJKZcUJeCtSpFmbJGKN45JmHuKjBdV8AhhMFTxB4Xo5oj2umwc08VAeEBzl0uouqPQnZ4V34ZL0sZlQEw3Jpg1miQ3gLF6YMzNNT4KrwAOfQ1Yj5t4+P3oHC1u3mJNALoVIZsjV9I9AcyFVAB4AVgfDIgDUBKaLSGnCs7SD2mMmlootoGjSDcA+72O7RQwXSQyQGMqbjrHMZV+RviFH/hP20cj/Gd6ET/xwb4A8CUMDSJ3MyIAAAAASUVORK5CYII=)IBMhttps://www.ibm.com\\n› think › topics › langchain](https://www.ibm.com/think/topics/langchain)\\n\\nIBM\\n\\nhttps://www.ibm.com › think › topics › langchain\\n\\nLangChain is essentially _a library of abstractions for Python and Javascript_\\n, representing common steps and concepts necessary to work with language\\nmodels.\\n\\n[  \\nWhat is\\nLangChain?![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAcElEQVR4AWP4//8/RZh6BgCZAkDsAMUNWDFCXgDFACCV8J/B+D8pGKwHRAKRAUyQDEMMQAYEUGBAAsiABpwKHjz4/9/BAZ8BDXgNgIMNGyg04MABkg1AeCEgAK8XKA5EiqORooSELykXEJuUBz43AgAIA1ZhBoG9vwAAAABJRU5ErkJggg==)YouTube\\n· IBM Technology287.6K+ views · 10 months\\nago](https://www.youtube.com/watch?v=1bUy-1hGZpI)\\n\\nYouTube · IBM Technology\\n\\n287.6K+ views · 10 months ago\\n\\nLang chain is _an open-source orchestration framework_ for the development of\\napplications that use large language models.\\n\\n[  \\nWhat is Langchain and why should I care as a\\ndeveloper?![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAQlBMVEVHcEwAAAAAAAAAAAAAAAAAAABxcXGkpKSUlJQeHh5/f3/Q0ND////e3t6rq6taWlrHx8e0tLQsLCw+Pj7u7u62trYTUwO8AAAABnRSTlMAS8D5/5dwkjMFAAAA1klEQVR4AX3TRQLEIAwFUNoGhypz/6vOJ9SFrAIPFyFE03b0iK5tBELSR0j0o89oRPuNrei+sRNUiYJKa20slXAoqBOSDyG4klqkns6oURNLapD2F+x7VA2cjvqOkwWOZfq+oPLTjiN0zh3nibHHGnYcgJpo8cTosIQdZ4pQJIoRpf6MjncTiRFL8H1/oE3YjTEFF972gZR3k2jH/oILL2kfNl2QsBu7Yl7eeEGF8oq8vLSi56NLA+d88D/ofmW5K5vqy5Upj56VqD+T6gOrPs3qo659hz8m8RNl7wTa8QAAAABJRU5ErkJggg==)Medium\\n· Logan Kilpatrick370+ likes · 1 year ago](https://medium.com/around-the-\\nprompt/what-is-langchain-and-why-should-i-care-as-a-developer-b2d952c42b28)\\n\\nMedium · Logan Kilpatrick\\n\\n370+ likes · 1 year ago\\n\\n _Langchain_ makes creating agents using large language models simple through\\ntheir agents API. Developers can use OpenAI functions or other means ...\\n\\n[  \\nLangChain![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAAAnklEQVR4AeTNIQiDQABG4b+u17X1aF6PK3YEO9iMJqPVau82y4FgMezS0oVLhqsHtrcqeqzDXv3CEz/6L4yTtZM3dnHmPTtjzXZAXKYVo4agkU2GI2Lloc6JDez1+flswMu1EQZ3xlE7lK8eKDkjtwE+crBMV+wesKmCiisGGepZIfQJpMj9SNb2MYWrChjVkULuCyCfRvsdmBieyQQAsoDk/9ryhFMAAAAASUVORK5CYII=)Wikipediahttps://en.wikipedia.org\\n› wiki › LangChain](https://en.wikipedia.org/wiki/LangChain)\\n\\nWikipedia\\n\\nhttps://en.wikipedia.org › wiki › LangChain\\n\\nLangChain is a software framework that helps facilitate the integration of\\nlarge language models (LLMs) into applications.\\n\\n\\u200e[History](https://en.wikipedia.org/wiki/LangChain#History) ·\\n\\u200e[Capabilities](https://en.wikipedia.org/wiki/LangChain#Capabilities) ·\\n\\u200e[LangChain tools](https://en.wikipedia.org/wiki/LangChain#LangChain_tools)\\n\\n[  \\nWhat Is LangChain? A Complete Comprehensive\\nOverview![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAMFBMVEX///////////8AAADNzc2/v7+np6eOjo7x8fGenp4mJibe3t5BQUFdXV1oaGh9fX0JTbfNAAAAAnRSTlP8WKsquk8AAAB7SURBVCiR1ZNLDoAgDAWhRSgf8f63lT8GhZULndWjk7ShAcYZTGCcTV2wCxfs76TdMhQLVA5VaiwIAFFzl4eMOCRCJzNdpiawR+mHmRcJrnS1TxKUSaTSTWYE6ia9ipggZUrKoxyvEgbVmbotQWSoZ/vCbr8ll4969R1OiO0IjOTl5agAAAAASUVORK5CYII=)DataStaxhttps://www.datastax.com\\n› guides › what-is-langchain](https://www.datastax.com/guides/what-is-\\nlangchain)\\n\\nDataStax\\n\\nhttps://www.datastax.com › guides › what-is-langchain\\n\\nNov 9, 2023 — LangChain is _a Python framework designed to streamline AI\\napplication development_ , focusing on real-time data processing and\\nintegration with ...\\n\\n[  \\nWhat Is\\nLangChain?![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAABp0lEQVR4AWJwL/ChKx4aFt5K9AFUW5cADYVRGIZxqxRcOu7uVnC33hPuW0+QiHgl4m6ZXnBouP7cDz1czj/X8M53nu26N7I8SICLwmSN0uFFQbKg4TW8h89YBMQwFSINnzUHBHZsKIauCmLFcUHugZGg6RjuK4YuRb729swoEL+SG0rW2TjC43+Y5lEUaG9EnvZ2ngWZf5aNL5/npr7Qe/yI295Af/Xn8RreoxgpSy+IL181xYnbseA32uumeybel4V/pMLQLg+SX4vhL6sugva86InQtVKJDCUQ6S6MBZVBEUpqQJaGB28HpSgDCmOS/MNEAFwUBDZpDMZtPAj/RAKiUQLqXmxYbzzGh+Gyf+mCrY/BJskAikZwgBFbbRYGtatBfhcwLgxnwHYORCUWAMtkYKIavF3027IAuMuAiexG87boIoBGTjXlJs1WhnNhi+TCUA5DdCvVUAz3pXMVInqmTiTN1P4rca6IHjcN7HbwB0TKPzpjMIuA9HT15zICKMEsAgLD7L8gKXGmehBDLQSOGnzGxwYDXBbWCd9Np1KZc1+XOhX4DttSLI3wbnoRAAAAAElFTkSuQmCC)Google\\nCloudhttps://cloud.google.com › use-cases ›\\nlangchain](https://cloud.google.com/use-cases/langchain)\\n\\nGoogle Cloud\\n\\nhttps://cloud.google.com › use-cases › langchain\\n\\n _LangChain_ is a programming language platform that lets developers construct\\nand connect models to access, transform, and share data seamlessly.\\n\\n\\u200e[Langchain And Ai](https://cloud.google.com/use-\\ncases/langchain#:~:text=LangChain%20and%20AI) · \\u200e[How Does Langchain\\nWork?](https://cloud.google.com/use-\\ncases/langchain#:~:text=How%20does%20LangChain%20work%3F) · \\u200e[Key Features Of\\nLangchain](https://cloud.google.com/use-\\ncases/langchain#:~:text=Key%20features%20of%20LangChain)\\n\\n# Page Navigation\\n\\n| 1|\\n[2](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=10&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAE)|\\n[3](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=20&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAG)|\\n[4](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=30&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAI)|\\n[5](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=40&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAK)|\\n[6](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=50&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAM)|\\n[7](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=60&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAO)|\\n[8](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=70&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAQ)|\\n[9](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=80&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAS)|\\n[10](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=90&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8tMDegQICBAU)|\\n[Next](/search?q=What+is+LangChain?&sca_esv=4c6b8dc13bab3e46&udm=14&ei=e8iJZ425Mabg0PEP6LmQGQ&start=10&sa=N&sstk=ATObxK4t7c6xZe8J3zQzlUfrNV-\\nBchujCI0GxH83wgy_vu9jEqYrHuTxd0wVBzubCa-bn_k1uK_Zn1BBIfr2yh6eyUzMdvUxFJ-\\nmCw&ved=2ahUKEwjN4oy74vuKAxUmMDQIHegcJAMQ8NMDegQICBAW)  \\n---|---|---|---|---|---|---|---|---|---|---|---  \\n  \\n# Footer Links\\n\\nWasco County, Oregon \\\\- From your IP address\\n\\n\\\\-\\n\\nUpdate location\\n\\nCan't update your locationLearn more\\n\\nUpdating location...\\n\\n[Help](https://support.google.com/websearch/?p=ws_results_help&hl=en&fg=1)Send\\nfeedback[Privacy](https://policies.google.com/privacy?hl=en&fg=1)[Terms](https://policies.google.com/terms?hl=en&fg=1)\\n\\n\\n\", error=None, include_in_memory=False), ActionResult(is_done=True, extracted_content='LangChain is a composable framework designed for building applications with large language models (LLMs). It simplifies the integration of language models with external data sources and is open-source, supported by an active community. LangChain provides tools for developers to streamline the application lifecycle of LLMs.', error=None, include_in_memory=False)], all_model_outputs=[{'search_google': {'query': 'What is LangChain?'}}, {'extract_content': {'include_links': True}}, {'done': {'text': 'LangChain is a composable framework designed for building applications with large language models (LLMs). It simplifies the integration of language models with external data sources and is open-source, supported by an active community. LangChain provides tools for developers to streamline the application lifecycle of LLMs.'}}])\n"]}], "source": ["# from browser_use import Agent\n", "import asyncio\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "from browser_use import Browser, BrowserConfig\n", "\n", "# Basic configuration\n", "config = BrowserConfig(\n", "\theadless=True,\n", "\t# disable_security=True\n", ")\n", "# Reuse existing browser\n", "browser = Browser(config=config)\n", "# async def main():\n", "agent = Agent(\n", "\ttask='what is langchain',\n", "\tllm=llm,\n", "\tbrowser=browser,\n", "\tgenerate_gif=False,  # Browser instance will be reused\n", ")\n", "\n", "result = await agent.run()\n", "print(result)\n", "# Manually close the browser\n", "# asyncio.run(main())\n", "await browser.close()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nKGC936xODry", "outputId": "de70d715-c30a-4d5b-9d25-40bd79d410de"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LangChain is a composable framework designed for building applications with large language models (LLMs). It simplifies the integration of language models with external data sources and is open-source, supported by an active community. LangChain provides tools for developers to streamline the application lifecycle of LLMs.\n"]}], "source": ["# display(result.action_results())\n", "for action in result.action_results():\n", "\tif action.is_done:\n", "\t\tprint(action.extracted_content)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}