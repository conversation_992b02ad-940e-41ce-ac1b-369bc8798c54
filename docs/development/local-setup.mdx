---
title: "Local Setup"
description: "Set up Browser Use development environment locally"
icon: "laptop-code"
---

## Prerequisites

Browser Use requires Python 3.11 or higher. We recommend using [uv](https://docs.astral.sh/uv/) for Python environment management.

## Clone the Repository

First, clone the Browser Use repository:

```bash
git clone https://github.com/browser-use/browser-use
cd browser-use
```

## Environment Setup

1. Create and activate a virtual environment:

```bash
uv venv --python 3.11
source .venv/bin/activate
```

2. Install dependencies:

```bash
# Install the package in editable mode with all development dependencies
uv sync
```

## Configuration

Set up your environment variables:

```bash
# Copy the example environment file
cp .env.example .env
```

Or manually create a `.env` file with the API key for the models you want to use set:

```bash .env
OPENAI_API_KEY=...
ANTHROPIC_API_KEY=
AZURE_ENDPOINT=
AZURE_OPENAI_API_KEY=
GEMINI_API_KEY=
DEEPSEEK_API_KEY=
```

<Note>
  You can use any LLM model supported by Lang<PERSON>hain. See 
  [LangChain Models](/customize/supported-models) for available options and their specific
  API key requirements.
</Note>

## Development

After setup, you can:

- Try demos in the example library with `uv run examples/simple.py`
- Run the linter/formatter with `uv run ruff format examples/some/file.py`
- Run tests with `uv run pytest`
- Build the package with `uv build`

## Getting Help

If you run into any issues:

1. Check our [GitHub Issues](https://github.com/browser-use/browser-use/issues)
2. Join our [Discord community](https://link.browser-use.com/discord) for support

<Note>
  We welcome contributions! See our [Contribution Guide](/development/contribution-guide) for guidelines on how to help improve
  Browser Use.
</Note>
