---
title: "Contribution Guide"
description: "Learn how to contribute to Browser Use"
icon: "code-pull-request"
---


- check out our most active issues or ask in [Discord](https://discord.gg/zXJJHtJf3k) for ideas of what to work on
- get inspiration / share what you build in the [`#showcase-your-work`](https://discord.com/channels/1303749220842340412/1305549200678850642) channel and on [`awesome-browser-use-prompts`](https://github.com/browser-use/awesome-prompts)!
- no typo/style-only nit PRs, you can submit nit fixes but only if part of larger bugfix or new feature PRs
- include a demo screenshot/gif, tests, and ideally an example script demonstrating any changes in your PR
- bump your issues/PRs with comments periodically if you want them to be merged faster
