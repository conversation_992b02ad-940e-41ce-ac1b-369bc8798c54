#!/usr/bin/env python3
"""
Script to fix the current UUID-named CSV file
"""

import os
import time

def fix_current_csv():
    """Rename the current UUID-based CSV file to a proper name"""
    downloads_dir = "/home/<USER>/10x-sales-agent/downloads"
    
    # Find the current UUID-based CSV file
    current_file = "/home/<USER>/10x-sales-agent/downloads/256f44d0-2c90-4b0f-9819-918d2a5f5f0b_1752677091.csv"
    
    if not os.path.exists(current_file):
        print(f"File not found: {current_file}")
        return
    
    # Create a descriptive name
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    new_filename = f"Seerfar-Product-Export-{timestamp}.csv"
    new_path = os.path.join(downloads_dir, new_filename)
    
    try:
        # Check file content to verify it's a valid CSV
        with open(current_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            line_count = sum(1 for _ in f) + 1  # +1 for the first line we already read
        
        print(f"Current file: {os.path.basename(current_file)}")
        print(f"File size: {os.path.getsize(current_file):,} bytes")
        print(f"Line count: {line_count:,}")
        print(f"Header: {first_line[:100]}...")
        
        # Rename the file
        os.rename(current_file, new_path)
        print(f"\n✅ Successfully renamed to: {new_filename}")
        print(f"New path: {new_path}")
        
        # Verify the new file
        if os.path.exists(new_path):
            print(f"✅ Verification: New file exists and is accessible")
        else:
            print(f"❌ Error: New file not found after rename")
            
    except Exception as e:
        print(f"❌ Error renaming file: {e}")

if __name__ == "__main__":
    fix_current_csv()
