#!/usr/bin/env python3
"""
<PERSON>ript to clean up duplicate CSV files in the downloads directory
Updated to work with new timestamp-processID folder structure
"""

import os
import glob
import hashlib
import shutil
from path_manager import PathManager, get_legacy_downloads_path, get_legacy_processed_path

def get_file_hash(file_path):
    """Calculate SHA256 hash of file content for duplicate detection"""
    try:
        with open(file_path, 'rb') as f:
            file_hash = hashlib.sha256()
            chunk = f.read(8192)
            while chunk:
                file_hash.update(chunk)
                chunk = f.read(8192)
        return file_hash.hexdigest()
    except Exception as e:
        print(f"Error calculating hash for {file_path}: {e}")
        return None

def get_file_info(file_path):
    """Get file information for comparison"""
    try:
        stat = os.stat(file_path)
        with open(file_path, 'r', encoding='utf-8') as f:
            # Count lines
            line_count = sum(1 for _ in f)
            f.seek(0)
            # Get first few characters to check content
            sample = f.read(200)
        
        return {
            'path': file_path,
            'size': stat.st_size,
            'mtime': stat.st_mtime,
            'lines': line_count,
            'sample': sample,
            'filename': os.path.basename(file_path)
        }
    except Exception as e:
        print(f"Error getting info for {file_path}: {e}")
        return None

def choose_best_file(duplicate_files):
    """Choose the best file from a list of duplicates"""
    file_infos = []
    for file_path in duplicate_files:
        info = get_file_info(file_path)
        if info:
            file_infos.append(info)
    
    if not file_infos:
        return None
    
    # Scoring criteria (higher is better):
    # 1. Prefer files with "Seerfar-Product" in name (more descriptive)
    # 2. Prefer files with date/time in name
    # 3. Prefer larger files (more complete)
    # 4. Prefer newer files
    
    def score_file(info):
        score = 0
        filename = info['filename'].lower()
        
        # Prefer descriptive names
        if 'seerfar-product' in filename:
            score += 100
        
        # Prefer files with date patterns
        if any(pattern in filename for pattern in ['20250716', '2025-07-16']):
            score += 50
        
        # Prefer files with "500" (indicating record count)
        if '500' in filename:
            score += 25
        
        # Prefer larger files (more content)
        score += info['size'] / 1000  # Size in KB as score component
        
        # Prefer newer files (small bonus)
        score += info['mtime'] / 1000000  # Timestamp as small score component
        
        return score
    
    # Sort by score (highest first)
    file_infos.sort(key=score_file, reverse=True)
    
    best_file = file_infos[0]
    print(f"Selected best file: {best_file['filename']} (score: {score_file(best_file):.2f})")
    print(f"  - Size: {best_file['size']} bytes")
    print(f"  - Lines: {best_file['lines']}")
    print(f"  - Sample: {best_file['sample'][:100]}...")
    
    return best_file['path']

def cleanup_duplicates(session_folder=None):
    """
    Main function to clean up duplicate CSV files

    Args:
        session_folder: Specific session folder to process, or None for legacy behavior
    """
    if session_folder:
        downloads_dir = session_folder
        print(f"Processing session folder: {session_folder}")
    else:
        downloads_dir = get_legacy_downloads_path()
        print(f"Processing legacy downloads directory: {downloads_dir}")
    
    print(f"Scanning for CSV files in: {downloads_dir}")
    
    # Find all CSV files in downloads directory (excluding processed subdirectory)
    csv_files = []
    for file_path in glob.glob(os.path.join(downloads_dir, "*.csv")):
        if os.path.isfile(file_path):
            csv_files.append(file_path)
    
    print(f"Found {len(csv_files)} CSV files:")
    for file_path in csv_files:
        print(f"  - {os.path.basename(file_path)}")
    
    if len(csv_files) <= 1:
        print("No duplicates to clean up.")
        return
    
    # Group files by content hash
    print(f"\nAnalyzing file contents...")
    hash_groups = {}
    
    for file_path in csv_files:
        file_hash = get_file_hash(file_path)
        if file_hash:
            if file_hash not in hash_groups:
                hash_groups[file_hash] = []
            hash_groups[file_hash].append(file_path)
    
    # Process each group of duplicates
    files_to_keep = []
    files_to_remove = []
    
    for file_hash, duplicate_files in hash_groups.items():
        if len(duplicate_files) == 1:
            # No duplicates for this content
            files_to_keep.append(duplicate_files[0])
            print(f"\nUnique file: {os.path.basename(duplicate_files[0])}")
        else:
            # Multiple files with same content
            print(f"\nFound {len(duplicate_files)} duplicate files:")
            for file_path in duplicate_files:
                print(f"  - {os.path.basename(file_path)}")
            
            # Choose the best file to keep
            best_file = choose_best_file(duplicate_files)
            if best_file:
                files_to_keep.append(best_file)
                for file_path in duplicate_files:
                    if file_path != best_file:
                        files_to_remove.append(file_path)
    
    # Summary
    print(f"\n" + "="*60)
    print(f"CLEANUP SUMMARY:")
    print(f"- Total CSV files found: {len(csv_files)}")
    print(f"- Unique content groups: {len(hash_groups)}")
    print(f"- Files to keep: {len(files_to_keep)}")
    print(f"- Files to remove: {len(files_to_remove)}")
    
    if files_to_keep:
        print(f"\nFiles to KEEP:")
        for file_path in files_to_keep:
            print(f"  ✓ {os.path.basename(file_path)}")
    
    if files_to_remove:
        print(f"\nFiles to REMOVE:")
        for file_path in files_to_remove:
            print(f"  ✗ {os.path.basename(file_path)}")
        
        # Ask for confirmation
        print(f"\nDo you want to proceed with removing {len(files_to_remove)} duplicate files? (y/N): ", end="")
        response = input().strip().lower()
        
        if response in ['y', 'yes']:
            # Create processed directory if it doesn't exist
            if session_folder:
                # For session folders, create processed subdirectory
                processed_dir = os.path.join(downloads_dir, "processed")
                os.makedirs(processed_dir, exist_ok=True)
            else:
                # For legacy behavior, use legacy processed path
                processed_dir = get_legacy_processed_path()
                os.makedirs(processed_dir, exist_ok=True)
            
            # Move duplicates to processed directory instead of deleting
            for file_path in files_to_remove:
                filename = os.path.basename(file_path)
                backup_path = os.path.join(processed_dir, f"duplicate_{filename}")
                try:
                    shutil.move(file_path, backup_path)
                    print(f"  Moved {filename} to processed/duplicate_{filename}")
                except Exception as e:
                    print(f"  Error moving {filename}: {e}")
            
            print(f"\nCleanup complete! {len(files_to_remove)} duplicate files moved to processed/ directory.")
            print(f"Remaining files in downloads:")
            for file_path in files_to_keep:
                print(f"  ✓ {os.path.basename(file_path)}")
        else:
            print("Cleanup cancelled.")
    else:
        print("\nNo duplicate files found!")

def cleanup_all_session_folders():
    """Clean up duplicates in all session folders"""
    base_downloads_dir = get_legacy_downloads_path()

    if not os.path.exists(base_downloads_dir):
        print(f"Base downloads directory does not exist: {base_downloads_dir}")
        return

    # Find all session folders
    session_folders = []
    for item in os.listdir(base_downloads_dir):
        item_path = os.path.join(base_downloads_dir, item)
        if os.path.isdir(item_path) and PathManager.is_session_folder(item):
            session_folders.append(item_path)

    if not session_folders:
        print("No session folders found. Processing legacy downloads directory.")
        cleanup_duplicates()
        return

    print(f"Found {len(session_folders)} session folders:")
    for folder in session_folders:
        print(f"  - {os.path.basename(folder)}")

    # Process each session folder
    for folder in session_folders:
        print(f"\n--- Processing {os.path.basename(folder)} ---")
        cleanup_duplicates(folder)

    # Also process legacy downloads directory for any remaining files
    print(f"\n--- Processing legacy downloads directory ---")
    cleanup_duplicates()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # Process specific session folder
        session_folder = sys.argv[1]
        if os.path.exists(session_folder):
            cleanup_duplicates(session_folder)
        else:
            print(f"Session folder not found: {session_folder}")
    else:
        # Process all session folders
        cleanup_all_session_folders()
