#!/usr/bin/env python3
"""
Final cleanup script to keep only one CSV file
"""

import os
import shutil

def final_cleanup():
    """Keep only the best CSV file and move the other to processed"""
    downloads_dir = "/home/<USER>/10x-sales-agent/downloads"
    
    # The two remaining files
    file1 = "/home/<USER>/10x-sales-agent/downloads/50b0d210-5f22-4f46-b775-0e576cbb02eb_1752629966.csv"
    file2 = "/home/<USER>/10x-sales-agent/downloads/Seerfar-Product20250716_500_1752629966.csv"
    
    print("Remaining CSV files:")
    print(f"1. {os.path.basename(file1)} - Size: {os.path.getsize(file1)} bytes")
    print(f"2. {os.path.basename(file2)} - Size: {os.path.getsize(file2)} bytes")
    
    # The second file has a better name (more descriptive)
    # But let's check which one is better
    
    print(f"\nFile 1 (UUID name): {os.path.basename(file1)}")
    print(f"  - Size: {os.path.getsize(file1)} bytes")
    print(f"  - Name quality: Poor (UUID-based)")
    
    print(f"\nFile 2 (Descriptive name): {os.path.basename(file2)}")
    print(f"  - Size: {os.path.getsize(file2)} bytes") 
    print(f"  - Name quality: Good (descriptive with date and record count)")
    
    # Choose the file with the better name (file2)
    file_to_keep = file2
    file_to_remove = file1
    
    print(f"\nRecommendation: Keep '{os.path.basename(file_to_keep)}' (better filename)")
    print(f"Will move '{os.path.basename(file_to_remove)}' to processed directory")
    
    # Ask for confirmation
    response = input(f"\nProceed with keeping only '{os.path.basename(file_to_keep)}'? (Y/n): ").strip().lower()
    
    if response in ['', 'y', 'yes']:
        # Create processed directory if it doesn't exist
        processed_dir = os.path.join(downloads_dir, "processed")
        os.makedirs(processed_dir, exist_ok=True)
        
        # Move the file with poor name to processed directory
        backup_path = os.path.join(processed_dir, f"backup_{os.path.basename(file_to_remove)}")
        try:
            shutil.move(file_to_remove, backup_path)
            print(f"✓ Moved {os.path.basename(file_to_remove)} to processed/backup_{os.path.basename(file_to_remove)}")
            
            # Optionally rename the kept file to a cleaner name
            final_name = "Seerfar-Product-Export-500-Records.csv"
            final_path = os.path.join(downloads_dir, final_name)
            
            if os.path.exists(final_path):
                print(f"Note: {final_name} already exists, keeping current name")
            else:
                shutil.move(file_to_keep, final_path)
                print(f"✓ Renamed kept file to: {final_name}")
                file_to_keep = final_path
            
            print(f"\n🎉 Cleanup complete!")
            print(f"Final result: 1 CSV file in downloads directory")
            print(f"📁 {os.path.basename(file_to_keep)}")
            
            # Show file info
            with open(file_to_keep, 'r', encoding='utf-8') as f:
                line_count = sum(1 for _ in f)
            
            print(f"\nFile details:")
            print(f"  - Size: {os.path.getsize(file_to_keep):,} bytes")
            print(f"  - Lines: {line_count:,} (including header)")
            print(f"  - Records: {line_count-1:,} data records")
            
        except Exception as e:
            print(f"❌ Error during cleanup: {e}")
    else:
        print("Cleanup cancelled.")

if __name__ == "__main__":
    final_cleanup()
