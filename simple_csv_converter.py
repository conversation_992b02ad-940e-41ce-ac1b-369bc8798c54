#!/usr/bin/env python3
"""
Simple CSV converter for downloaded files without external dependencies
Updated to work with new timestamp-processID folder structure
"""

import os
import glob
import csv
import hashlib
from path_manager import PathManager, get_legacy_downloads_path

def get_file_hash(file_path):
    """Calculate SHA256 hash of file content for duplicate detection"""
    try:
        with open(file_path, 'rb') as f:
            file_hash = hashlib.sha256()
            chunk = f.read(8192)
            while chunk:
                file_hash.update(chunk)
                chunk = f.read(8192)
        return file_hash.hexdigest()
    except Exception as e:
        print(f"Error calculating hash for {file_path}: {e}")
        return None

def convert_to_csv_simple(file_path):
    """Simple CSV conversion without pandas"""
    print(f"Processing file: {file_path}")
    
    base_name = os.path.splitext(file_path)[0]
    csv_path = f"{base_name}.csv"
    
    try:
        # Check if file already has content that looks like CSV
        with open(file_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            
        # If it looks like CSV (has commas and typical CSV headers), just rename it
        if ',' in first_line and any(header in first_line.lower() for header in ['title', 'price', 'sku', 'url', 'no.']):
            print(f"File appears to be CSV format, copying to: {csv_path}")
            
            # Copy content to new file with .csv extension
            with open(file_path, 'r', encoding='utf-8') as src:
                with open(csv_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            
            # Remove original file
            os.remove(file_path)
            print(f"Successfully converted {file_path} to {csv_path}")
            return csv_path
        else:
            print(f"File doesn't appear to be CSV format, keeping original: {file_path}")
            return file_path
            
    except Exception as e:
        print(f"Error converting {file_path}: {e}")
        return file_path

def process_files(session_folder=None):
    """
    Process all files in downloads directory

    Args:
        session_folder: Specific session folder to process, or None for legacy behavior
    """
    if session_folder:
        downloads_dir = session_folder
        print(f"Processing session folder: {session_folder}")
    else:
        downloads_dir = get_legacy_downloads_path()
        print(f"Processing legacy downloads directory: {downloads_dir}")
    
    print(f"Checking for files in: {downloads_dir}")
    
    if not os.path.exists(downloads_dir):
        print(f"Downloads directory does not exist: {downloads_dir}")
        return
    
    # Find all files in downloads directory, excluding CSV files and processed subdirectory
    pattern = os.path.join(downloads_dir, "*")
    all_items = glob.glob(pattern)
    existing_files = [f for f in all_items if os.path.isfile(f) and not f.endswith('.csv') and 'processed' not in f]
    
    print(f"Found {len(existing_files)} files to process:")
    for file_path in existing_files:
        print(f"  - {file_path}")
    
    if not existing_files:
        print("No files found to process")
        return
    
    # Process each file
    processed_files = []
    for file_path in existing_files:
        converted_path = convert_to_csv_simple(file_path)
        processed_files.append(converted_path)
    
    # Remove duplicates based on content hash
    print(f"\nChecking for duplicates among {len(processed_files)} files...")
    unique_files = []
    file_hashes = {}
    duplicates_removed = []
    
    for file_path in processed_files:
        if not os.path.exists(file_path):
            continue
            
        file_hash = get_file_hash(file_path)
        if file_hash is None:
            unique_files.append(file_path)
            continue
        
        if file_hash in file_hashes:
            # Duplicate found
            existing_file = file_hashes[file_hash]
            print(f"Duplicate detected: {file_path} is identical to {existing_file}")
            
            # Remove the duplicate
            try:
                os.remove(file_path)
                duplicates_removed.append(file_path)
                print(f"Removed duplicate: {file_path}")
            except OSError as e:
                print(f"Could not remove duplicate {file_path}: {e}")
                unique_files.append(file_path)
        else:
            file_hashes[file_hash] = file_path
            unique_files.append(file_path)
    
    print(f"\nProcessing complete!")
    print(f"- Original files: {len(existing_files)}")
    print(f"- Final unique files: {len(unique_files)}")
    print(f"- Duplicates removed: {len(duplicates_removed)}")
    
    if unique_files:
        print(f"\nFinal processed files:")
        for file_path in unique_files:
            print(f"  - {file_path}")

def process_all_session_folders():
    """Process all session folders in the downloads directory"""
    base_downloads_dir = get_legacy_downloads_path()

    if not os.path.exists(base_downloads_dir):
        print(f"Base downloads directory does not exist: {base_downloads_dir}")
        return

    # Find all session folders
    session_folders = []
    for item in os.listdir(base_downloads_dir):
        item_path = os.path.join(base_downloads_dir, item)
        if os.path.isdir(item_path) and PathManager.is_session_folder(item):
            session_folders.append(item_path)

    if not session_folders:
        print("No session folders found. Processing legacy downloads directory.")
        process_files()
        return

    print(f"Found {len(session_folders)} session folders:")
    for folder in session_folders:
        print(f"  - {os.path.basename(folder)}")

    # Process each session folder
    for folder in session_folders:
        print(f"\n--- Processing {os.path.basename(folder)} ---")
        process_files(folder)

    # Also process legacy downloads directory for any remaining files
    print(f"\n--- Processing legacy downloads directory ---")
    process_files()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # Process specific session folder
        session_folder = sys.argv[1]
        if os.path.exists(session_folder):
            process_files(session_folder)
        else:
            print(f"Session folder not found: {session_folder}")
    else:
        # Process all session folders
        process_all_session_folders()
