#!/usr/bin/env python3
"""
Script to move the current CSV file to processed directory
Updated to work with new timestamp-processID folder structure
"""

import os
import shutil
import glob
from path_manager import PathManager, get_legacy_downloads_path, get_legacy_processed_path

def move_csv_to_processed(session_folder=None):
    """
    Move any CSV files in downloads to processed directory

    Args:
        session_folder: Specific session folder to process, or None for legacy behavior
    """
    if session_folder:
        # Process specific session folder
        downloads_dir = session_folder
        processed_dir = os.path.join(session_folder, "processed")
        print(f"Processing session folder: {session_folder}")
    else:
        # Legacy behavior - process main downloads directory
        downloads_dir = get_legacy_downloads_path()
        processed_dir = get_legacy_processed_path()
        print(f"Processing legacy downloads directory: {downloads_dir}")
    
    print(f"Checking for CSV files in: {downloads_dir}")
    
    # Create processed directory if it doesn't exist
    os.makedirs(processed_dir, exist_ok=True)
    print(f"Ensured processed directory exists: {processed_dir}")
    
    # Find all CSV files in downloads directory (excluding processed subdirectory)
    csv_files = []
    for file in glob.glob(os.path.join(downloads_dir, "*.csv")):
        if 'processed' not in file and os.path.isfile(file):
            csv_files.append(file)
    
    print(f"Found {len(csv_files)} CSV files to move:")
    for file in csv_files:
        print(f"  - {os.path.basename(file)}")
    
    if not csv_files:
        print("No CSV files found to move.")
        return
    
    # Move each CSV file to processed directory
    moved_files = []
    for file_path in csv_files:
        try:
            filename = os.path.basename(file_path)
            processed_file_path = os.path.join(processed_dir, filename)
            
            # Check if file already exists in processed directory
            if os.path.exists(processed_file_path):
                # Add timestamp to avoid conflicts
                import time
                timestamp = int(time.time())
                name, ext = os.path.splitext(filename)
                new_filename = f"{name}_{timestamp}{ext}"
                processed_file_path = os.path.join(processed_dir, new_filename)
                print(f"  File exists, using new name: {new_filename}")
            
            # Move the file
            shutil.move(file_path, processed_file_path)
            moved_files.append(processed_file_path)
            print(f"  ✅ Moved {filename} to processed/{os.path.basename(processed_file_path)}")
            
        except Exception as e:
            print(f"  ❌ Error moving {os.path.basename(file_path)}: {e}")
    
    print(f"\n🎉 Successfully moved {len(moved_files)} files to processed directory:")
    for file_path in moved_files:
        print(f"  📁 {os.path.basename(file_path)}")
    
    # Verify downloads directory is now clean
    remaining_csv = glob.glob(os.path.join(downloads_dir, "*.csv"))
    remaining_csv = [f for f in remaining_csv if 'processed' not in f]
    
    if remaining_csv:
        print(f"\n⚠️  Warning: {len(remaining_csv)} CSV files still remain in downloads:")
        for file in remaining_csv:
            print(f"  - {os.path.basename(file)}")
    else:
        print(f"\n✅ Downloads directory is now clean of CSV files")

def process_all_session_folders():
    """Process all session folders in the downloads directory"""
    base_downloads_dir = get_legacy_downloads_path()

    if not os.path.exists(base_downloads_dir):
        print(f"Base downloads directory does not exist: {base_downloads_dir}")
        return

    # Find all session folders
    session_folders = []
    for item in os.listdir(base_downloads_dir):
        item_path = os.path.join(base_downloads_dir, item)
        if os.path.isdir(item_path) and PathManager.is_session_folder(item):
            session_folders.append(item_path)

    if not session_folders:
        print("No session folders found. Processing legacy downloads directory.")
        move_csv_to_processed()
        return

    print(f"Found {len(session_folders)} session folders:")
    for folder in session_folders:
        print(f"  - {os.path.basename(folder)}")

    # Process each session folder
    for folder in session_folders:
        print(f"\n--- Processing {os.path.basename(folder)} ---")
        move_csv_to_processed(folder)

    # Also process legacy downloads directory for any remaining files
    print(f"\n--- Processing legacy downloads directory ---")
    move_csv_to_processed()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # Process specific session folder
        session_folder = sys.argv[1]
        if os.path.exists(session_folder):
            move_csv_to_processed(session_folder)
        else:
            print(f"Session folder not found: {session_folder}")
    else:
        # Process all session folders
        process_all_session_folders()
